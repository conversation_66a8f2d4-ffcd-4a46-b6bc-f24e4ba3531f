import React from "react";
import ReactPlayer from "react-player";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

export default function VideoDialog({
  children,
  videoUrl,
}: {
  children: React.ReactNode;
  videoUrl?: string;
}) {
  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="inset-10 max-w-full max-h-[740px]">
        <DialogTitle className="sr-only">Video</DialogTitle>
        <ReactPlayer src={videoUrl} width="100%" height="100%" playing={true} />
      </DialogContent>
    </Dialog>
  );
}
