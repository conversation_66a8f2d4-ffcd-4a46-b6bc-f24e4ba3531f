"use client";

import React from "react";
import Video from "next-video";
import BackgroundVideo from "next-video/background-video";
import { cn } from "@/lib/utils";
import type { MaxResolutionValue } from "@mux/playback-core";
import type { StaticImageData } from "next/image";
import Image from "next/image";
import type { VideoProps as MuxVideoProps } from "next-video";
import type { MuxVideoAsset, MuxVideoObject } from "../../../sanity.types";
import { stegaClean } from "next-sanity";

export type VideoData = MuxVideoProps & {
  video?: MuxVideoObject & { asset: MuxVideoAsset };
  poster?: StaticImageData;
  maxResolution?: MaxResolutionValue;
  aspectRatio?: string;
  existingAssetId?: string;
};

interface NextVideoPlayerProps {
  video: VideoData;
  className?: string;
  isBackground?: boolean;
  overlayType?: "none" | "dark" | "gradient";
  children?: React.ReactNode;
  style?: React.CSSProperties;
  thumbnailTime?: number;
}

/**
 * Get Mux playback ID from video data
 */
function getPlaybackId(video: VideoData): string | null {
  if (video.video?.asset) {
    const asset = video.video.asset;

    // Try multiple sources for playback ID
    const candidates = [
      asset.playbackId,
      asset.data?.playback_ids?.find((p) => p.policy === "public")?.id,
      asset.data?.playback_ids?.[0]?.id,
      asset.assetId,
    ].filter(Boolean);

    return candidates[0] || null;
  }

  if (video.existingAssetId) return video.existingAssetId;

  return null;
}

/**
 * Convert video data to next-video compatible source
 */
function getVideoSource(video: VideoData, thumbnailTime?: number): URL | null {
  const playbackId = getPlaybackId(video);
  if (!playbackId) return null;

  // Create a Mux URL that next-video can process
  const baseUrl = `https://stream.mux.com/${playbackId}.m3u8`;

  if (thumbnailTime !== undefined) {
    return new URL(`${baseUrl}?thumbnailTime=${thumbnailTime}`);
  }

  return new URL(baseUrl);
}

/**
 * Next Video Player Component
 *
 * Uses next-video library for optimal video handling with Mux:
 * - Automatic optimization and CDN delivery
 * - Built-in poster generation
 * - Responsive and accessible player
 * - Background video support
 */
export default function NextVideoPlayer({
  video,
  className,
  isBackground = false,
  overlayType = "none",
  children,
  style,
  thumbnailTime = 10,
}: NextVideoPlayerProps) {
  const videoSrc = getVideoSource(video, thumbnailTime);
  const playbackId = getPlaybackId(video);

  if (!videoSrc?.href || !playbackId) {
    return (
      <div
        className={cn(
          "flex items-center justify-center bg-muted rounded-lg h-full",
          "aspect-video border border-dashed",
          className,
        )}
      >
        <div className="text-center p-4">
          <div className="text-muted-foreground text-sm mb-1">
            Video not available
          </div>
          {video.title && (
            <div className="text-xs text-muted-foreground">{video.title}</div>
          )}
        </div>
      </div>
    );
  }

  const getAspectRatioClass = (ratio?: string) => {
    switch (stegaClean(ratio)) {
      case "16:9":
        return "aspect-video";
      case "4:3":
        return "aspect-[4/3]";
      case "1:1":
        return "aspect-square";
      case "9:16":
        return "aspect-[9/16]";
      default:
        return "aspect-video";
    }
  };

  const getMuxAspectRatio = (ratio?: string) => {
    switch (stegaClean(ratio)) {
      case "16:9":
        return "16/9";
      case "4:3":
        return "4/3";
      case "1:1":
        return "1/1";
      case "9:16":
        return "9/16";
      default:
        return "auto";
    }
  };

  // Generate thumbnail URL using Mux Image API
  const thumbnailUrl =
    video.poster?.src ||
    `https://image.mux.com/${playbackId}/thumbnail.png?width=1280&height=720&time=${thumbnailTime}&fit_mode=smartcrop`;

  // Video props for next-video
  const videoProps = {
    ...video,
    //src: videoSrc?.href,
    poster: thumbnailUrl,
    controls: isBackground ? false : (video.controls ?? true),
    autoPlay: isBackground ? true : (video.autoPlay ?? false),
    loop: isBackground ? true : (video.loop ?? false),
    muted: isBackground ? true : (video.muted ?? false),
    playsInline: true,
    "aria-label": stegaClean(video.title) || "Video player",
    style: {
      "--media-primary-color": "#fdaff3",
      "--media-secondary-color": "#ff0088",
      "--media-accent-color": "#42ffe0",
      ...style,
    } as React.CSSProperties,
    // Mux-specific props
    playbackId,
    envKey: process.env.NEXT_PUBLIC_MUX_ENV_KEY,
    metadataVideoTitle: stegaClean(video.title),
    maxResolution: stegaClean(video.maxResolution),
    aspectRatio: getMuxAspectRatio(video.aspectRatio),
  };

  // Render background video
  if (isBackground) {
    return (
      <div
        className={cn(
          "relative flex justify-center items-center w-full h-full min-h-140 overflow-hidden",
          getAspectRatioClass(video.aspectRatio),
          className,
          {
            "-my-[8%]": getMuxAspectRatio(video.aspectRatio) === "16/9",
          },
        )}
      >
        <BackgroundVideo
          {...videoProps}
          title={stegaClean(video.title)}
          className="w-full h-full min-w-220 max-w-full object-cover [&_img]:w-full"
          style={{
            aspectRatio: getMuxAspectRatio(video.aspectRatio) ?? "16/9",
          }}
        >
          {/* Overlay effects */}
          {overlayType === "dark" && (
            <>
              <div className="absolute inset-0 bg-black/30 hover:bg-black/50 backdrop-blur-[1.5px] hover:backdrop-blur-[1.8px] transition-all duration-800" />
            </>
          )}

          {overlayType === "gradient" && (
            <>
              <div className="absolute inset-0 bg-gradient-to-tr from-black/60 to-transparent" />
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/40 to-transparent h-1/2" />
            </>
          )}

          {/* Content overlay */}
          {children && (
            <div className="relative z-10 h-full flex flex-col justify-center">
              {children}
            </div>
          )}
        </BackgroundVideo>

        {/* Accessibility: Hidden title for screen readers */}
        {video.title && <h2 className="sr-only">{video.title}</h2>}
      </div>
    );
  }

  // Render standard video player
  return (
    <div
      className={cn(
        "relative w-full overflow-hidden rounded-lg",
        getAspectRatioClass(video.aspectRatio),
        className,
      )}
    >
      <Video {...videoProps} className="w-full h-full" />

      {/* Video metadata for screen readers */}
      {video.title && <div className="sr-only">Video: {video.title}</div>}
    </div>
  );
}

/**
 * Enhanced Background Video Component
 * Wrapper for next-video BackgroundVideo with additional overlay options
 */
export function NextBackgroundVideo({
  video,
  className,
  overlayType = "dark",
  children,
  thumbnailTime = 0,
  style,
}: {
  video: VideoData;
  className?: string;
  overlayType: "none" | "dark" | "gradient" | null;
  children?: React.ReactNode;
  thumbnailTime?: number;
  style?: React.CSSProperties;
}) {
  return (
    <NextVideoPlayer
      video={video}
      isBackground={true}
      overlayType={overlayType!}
      className={className}
      thumbnailTime={thumbnailTime}
      style={style}
    >
      {children}
    </NextVideoPlayer>
  );
}

/**
 * Video Thumbnail Component
 * Uses next-video for generating optimized thumbnails with lazy video loading
 */
export function NextVideoThumbnail({
  video,
  className,
  onClickAction,
  showPlayButton = true,
  thumbnailTime = 0,
  playTrigger = "click",
}: {
  video: VideoData;
  className?: string;
  onClickAction?: () => void;
  showPlayButton?: boolean;
  thumbnailTime?: number;
  playTrigger?: "click" | "hover";
}) {
  const [isVideoLoaded, setIsVideoLoaded] = React.useState(false);
  const [isPlaying, setIsPlaying] = React.useState(false);
  const videoRef = React.useRef<HTMLVideoElement>(null); // Ref for accessing Mux player
  const videoWrapperRef = React.useRef<HTMLDivElement>(null); // Ref for wrapper element
  const playbackId = getPlaybackId(video);

  if (!playbackId) {
    return (
      <div
        className={cn(
          "flex items-center justify-center bg-muted rounded-lg",
          "aspect-video border border-dashed",
          className,
        )}
      >
        <div className="text-center p-2">
          <div className="text-muted-foreground text-xs">
            No thumbnail available
          </div>
        </div>
      </div>
    );
  }

  // Generate thumbnail URL using Mux Image API
  const thumbnailUrl =
    video.poster?.src ||
    `https://image.mux.com/${playbackId}/thumbnail.png?width=1280&height=720&time=${thumbnailTime}&fit_mode=smartcrop`;

  // Handle play button click - lazy load video
  const handlePlayClick = async (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsVideoLoaded(true);
    onClickAction?.();

    if (videoRef.current) {
      try {
        // Access underlying video element via media.nativeEl (from Mux docs)
        if (videoRef.current.play) {
          videoRef.current.play();
        }
        // Fallback: try accessing from parent video element
        else {
          const videoElement = videoRef.current.querySelector("mux-video");
          if (videoElement) {
            await (videoElement as HTMLVideoElement).play();
          }
        }
        setIsPlaying(true);
      } catch (error) {
        console.warn("Could not play video:", error);
      }
    }
  };

  const handlePauseClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    // Pause video using Mux documentation approach
    if (videoRef.current) {
      try {
        // Access underlying video element via media.nativeEl (from Mux docs)
        if (videoRef.current.pause) {
          videoRef.current.pause();
        }
        // Fallback: try accessing from parent video element
        else {
          // select video element from Mux player elementType "mux-video"
          const videoElement = videoRef.current.querySelector("mux-video");
          if (videoElement) {
            (videoElement as HTMLVideoElement).pause();
          }
        }
        setIsPlaying(false);
      } catch (error) {
        console.warn("Could not pause video:", error);
      }
    }
    setIsVideoLoaded(false);
  };

  // Handle mouse leave for hover trigger
  const handleMouseLeave = () => {
    if (playTrigger === "hover" && isVideoLoaded) {
      handlePauseClick({ stopPropagation: () => {} } as React.MouseEvent);
    }
  };

  // Show thumbnail with lazy load functionality
  return (
    <div
      className={cn(
        "relative cursor-pointer group overflow-hidden rounded-lg",
        "aspect-video bg-muted",
        className,
      )}
      onClick={
        playTrigger === "click"
          ? isVideoLoaded
            ? handlePlayClick
            : handlePauseClick
          : undefined
      }
      onMouseEnter={playTrigger === "hover" ? handlePlayClick : undefined}
      onMouseLeave={playTrigger === "hover" ? handleMouseLeave : undefined}
    >
      <div
        className={cn(
          "relative overflow-hidden rounded-lg",
          "aspect-video bg-muted",
          className,
          {
            hidden: !isPlaying,
          },
        )}
        ref={videoWrapperRef}
      >
        <NextVideoPlayer
          video={{
            ...video,
            autoplay: true,
            ref: videoRef,
          }}
          className="w-full h-full"
        />
      </div>
      <div
        className={cn(
          "relative overflow-hidden rounded-lg",
          "aspect-video bg-muted",
          className,
          {
            hidden: isPlaying,
          },
        )}
      >
        <Image
          src={thumbnailUrl}
          alt={video.title || "Video thumbnail"}
          className="w-full h-full object-cover transition-transform group-hover:scale-105"
          loading="lazy"
          fill
        />

        {/* Play button overlay */}
        {showPlayButton && (
          <div
            className="absolute inset-0 flex items-center justify-center bg-black/20 backdrop-blur-[1.5px]"
            onClick={handlePlayClick}
          >
            <div className="w-16 h-16 bg-black/50 rounded-full flex items-center justify-center group-hover:bg-black/70 transition-colors backdrop-blur-sm">
              <svg
                className="w-8 h-8 text-white ml-1"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path d="M8 5v14l11-7z" />
              </svg>
            </div>
          </div>
        )}

        {/* Video title overlay */}
        {video.title && (
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4">
            <h3 className="text-white text-sm font-medium truncate">
              {video.title}
            </h3>
          </div>
        )}
      </div>
    </div>
  );
}
