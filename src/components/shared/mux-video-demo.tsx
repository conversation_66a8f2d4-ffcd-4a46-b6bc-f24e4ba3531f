"use client";

import React, { useState } from "react";
import NextVideoPlayer, {
  NextBackgroundVideo,
  NextVideoThumbnail,
} from "./next-video-player";

// Demo component to showcase next-video integration with Mux
export default function MuxVideoDemo() {
  const [selectedVideo, setSelectedVideo] = useState<number>(0);

  // Example video configurations for testing
  const demoVideos = [
    {
      videoSource: "existing" as const,
      existingAssetId: "z6N00sQjcyNO3xIneWbH51502yO9h5vE1DSyXEAwC5IZY",
      title: "Demo Video 1",
      description: "Official Mux demo video with next-video optimization",
      controls: true,
      autoplay: false,
      loop: false,
      muted: true,
      aspectRatio: "16:9",
      maxResolution: "720p" as const,
      captions: true,
      language: "en",
      thumbnailTime: 10,
    },
    {
      videoSource: "existing" as const,
      existingAssetId: "z6N00sQjcyNO3xIneWbH51502yO9h5vE1DSyXEAwC5IZY",
      title: "Demo Video 2",
      description: "Same demo video with different settings for testing",
      controls: true,
      autoplay: false,
      loop: true,
      muted: true,
      aspectRatio: "16:9",
      maxResolution: "720p" as const,
      captions: false,
      thumbnailTime: 5,
    },
  ];

  return (
    <div className="space-y-8 p-8">
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-2">
          Next-Video + Mux Integration Demo
        </h2>
        <p className="text-muted-foreground">
          Professional video integration using next-video library with Mux CDN
        </p>
      </div>

      {/* Video Player */}
      <div className="max-w-4xl mx-auto">
        <NextVideoPlayer
          video={demoVideos[selectedVideo]}
          className="rounded-lg border"
          style={{
            "--media-primary-color": "#0ea5e9",
            "--media-secondary-color": "#0284c7",
            "--media-accent-color": "#06b6d4",
          }}
        />
      </div>

      {/* Video Selection */}
      <div className="max-w-4xl mx-auto">
        <h3 className="text-lg font-semibold mb-4">Available Videos</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {demoVideos.map((video, index) => (
            <div
              key={index}
              className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                selectedVideo === index
                  ? "border-primary bg-primary/5"
                  : "border-border hover:border-primary/50"
              }`}
              onClick={() => setSelectedVideo(index)}
            >
              <NextVideoThumbnail
                video={{
                  ...video,
                  controls: false,
                }}
                className="mb-3"
                onClickAction={() => setSelectedVideo(index)}
                thumbnailTime={video.thumbnailTime ?? 10}
                playTrigger="hover"
              />
              <h4 className="font-medium">{video.title}</h4>
              <p className="text-sm text-muted-foreground mt-1">
                {video.description}
              </p>
              <div className="flex flex-wrap gap-2 mt-2">
                <span className="text-xs bg-secondary px-2 py-1 rounded">
                  {video.maxResolution}
                </span>
                <span className="text-xs bg-secondary px-2 py-1 rounded">
                  {video.aspectRatio}
                </span>
                <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                  next-video
                </span>
                {video.captions && (
                  <span className="text-xs bg-secondary px-2 py-1 rounded">
                    CC
                  </span>
                )}
                {video.autoplay && (
                  <span className="text-xs bg-secondary px-2 py-1 rounded">
                    Autoplay
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Background Video Demo */}
      <div className="max-w-4xl mx-auto">
        <h3 className="text-lg font-semibold mb-4">Background Video Demo</h3>
        <div className="relative aspect-video rounded-lg overflow-hidden">
          <NextBackgroundVideo
            video={{
              ...demoVideos[0],
              autoplay: true,
              muted: true,
              loop: true,
            }}
            overlayType="dark"
            thumbnailTime={5}
          >
            {/* Demo content overlay */}
            <div className="flex items-center justify-center text-center text-white">
              <div>
                <h2 className="text-3xl font-bold mb-2">Hero Section</h2>
                <p className="text-lg opacity-90">
                  Professional video background powered by next-video + Mux
                </p>
                <div className="mt-4">
                  <span className="inline-block bg-white/20 backdrop-blur-sm px-3 py-1 rounded-full text-sm">
                    Auto-optimized • CDN Delivered • Responsive
                  </span>
                </div>
              </div>
            </div>
          </NextBackgroundVideo>
        </div>
      </div>

      {/* Gradient Overlay Demo */}
      <div className="max-w-4xl mx-auto">
        <h3 className="text-lg font-semibold mb-4">Gradient Overlay Demo</h3>
        <div className="relative aspect-video rounded-lg overflow-hidden w-full h-full">
          <NextBackgroundVideo
            video={{
              ...demoVideos[1],
              autoplay: true,
              muted: true,
              loop: true,
            }}
            overlayType="gradient"
            thumbnailTime={15}
          />
          <div className="absolute bottom-4 hover:bottom-6 left-0 right-0 p-4 hover:pt-5 backdrop-blur-sm text-white margin-auto bg-black/10 hover:b-black/60 transition-all duration-500 ">
            <h3 className="text-2xl font-bold mb-2">Gradient Overlay</h3>
            <p className="text-white/90">
              Elegant gradient overlays for better text readability over video
              backgrounds.
            </p>
          </div>
        </div>
      </div>

      {/* Technical Details */}
      <div className="max-w-4xl mx-auto text-sm text-muted-foreground">
        <h3 className="text-lg font-semibold mb-2 text-foreground">
          next-video Features
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <ul className="space-y-1">
            <li>✅ Automatic Mux integration with premium quality</li>
            <li>✅ Built-in poster image generation</li>
            <li>✅ Adaptive bitrate streaming (HLS)</li>
            <li>✅ Timeline hover thumbnails</li>
            <li>✅ Responsive video delivery</li>
            <li>✅ Custom player themes support</li>
          </ul>
          <ul className="space-y-1">
            <li>✅ Background video optimization</li>
            <li>✅ Environment-configurable URLs</li>
            <li>✅ Analytics integration (Mux Data)</li>
            <li>✅ Accessibility with ARIA labels</li>
            <li>✅ TypeScript support</li>
            <li>✅ Zero-config setup</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
