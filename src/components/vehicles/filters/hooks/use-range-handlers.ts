"use client";

import { useCallback } from "react";

/**
 * Hook for managing range filter event handlers
 * Single Responsibility: Handle user interactions (slider, inputs)
 */
export function useRangeHandlers<T extends number>({
  localRange,
  setLocalRange,
  inputValues,
  setInputValues,
  minValue,
  maxValue,
  updateFilters,
  debouncedUpdate,
}: {
  localRange: [T, T];
  setLocalRange: (range: [T, T]) => void;
  inputValues: { from: string; to: string };
  setInputValues: (values: { from: string; to: string }) => void;
  minValue: T;
  maxValue: T;
  updateFilters: (from: T, to: T) => void;
  debouncedUpdate: (from: T, to: T) => void;
}) {
  // Handle slider change - immediate UI update, context update only on release
  const handleSliderChange = useCallback(
    (values: number[]) => {
      const [from, to] = values as [T, T];
      setLocalRange([from, to]);
      setInputValues({
        from: from.toString(),
        to: to.toString(),
      });
      // Don't call context immediately - let user finish dragging
    },
    [setLocalRange, setInputValues],
  );

  // Handle slider commit (when user releases) - single API call
  const handleSliderCommit = useCallback(
    (values: number[]) => {
      const [from, to] = values as [T, T];
      updateFilters(from, to);
    },
    [updateFilters],
  );

  // Handle input change
  const handleInputChange = useCallback(
    (type: "from" | "to", value: string) => {
      // Update the input value immediately for responsive UI
      setInputValues({
        ...inputValues,
        [type]: value,
      });

      // Parse and validate the input value
      const numValue = parseInt(value, 10) as T;
      if (isNaN(numValue)) return;

      // Update local range based on input
      if (type === "from") {
        const validFrom = Math.max(
          minValue,
          Math.min(numValue, localRange[1]),
        ) as T;
        setLocalRange([validFrom, localRange[1]]);
        debouncedUpdate(validFrom, localRange[1]);
      } else {
        const validTo = Math.min(
          maxValue,
          Math.max(numValue, localRange[0]),
        ) as T;
        setLocalRange([localRange[0], validTo]);
        debouncedUpdate(localRange[0], validTo);
      }
    },
    [
      inputValues,
      setInputValues,
      localRange,
      setLocalRange,
      minValue,
      maxValue,
      debouncedUpdate,
    ],
  );

  // Handle input blur (validate and format)
  const handleInputBlur = useCallback(
    (type: "from" | "to") => {
      const value = parseInt(inputValues[type], 10) as T;

      if (isNaN(value)) {
        // Reset to current range value if invalid
        setInputValues({
          ...inputValues,
          [type]: (type === "from" ? localRange[0] : localRange[1]).toString(),
        });
        return;
      }

      if (type === "from") {
        const validFrom = Math.max(
          minValue,
          Math.min(value, localRange[1]),
        ) as T;
        setInputValues({ ...inputValues, from: validFrom.toString() });
        setLocalRange([validFrom, localRange[1]]);
        debouncedUpdate(validFrom, localRange[1]);
      } else {
        const validTo = Math.min(maxValue, Math.max(value, localRange[0])) as T;
        setInputValues({ ...inputValues, to: validTo.toString() });
        setLocalRange([localRange[0], validTo]);
        debouncedUpdate(localRange[0], validTo);
      }
    },
    [
      inputValues,
      setInputValues,
      localRange,
      setLocalRange,
      minValue,
      maxValue,
      debouncedUpdate,
    ],
  );

  return {
    handleSliderChange,
    handleSliderCommit,
    handleInputChange,
    handleInputBlur,
  };
}
