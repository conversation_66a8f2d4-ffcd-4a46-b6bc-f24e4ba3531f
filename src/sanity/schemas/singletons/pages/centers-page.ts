import { defineField, defineType } from "sanity";
import { fieldsets } from "../../misc/fieldsets";
import { fieldGroups } from "../../misc/field-groups";
import { centersPageDict } from "../../../dictionary/studio/schemas/singletons/pages/centers-page";

const { document, fields, descriptions } = centersPageDict;

export default defineType({
  name: document.name,
  title: document.title,
  type: "document",
  fieldsets: [...fieldsets],
  groups: [...fieldGroups],
  fields: [
    defineField({
      name: "title",
      title: fields.title,
      type: "string",
      description: descriptions.title,
    }),
    defineField({
      name: "slug",
      title: fields.slug,
      type: "slug",
      options: {
        source: "title",
      },
      description: descriptions.slug,
    }),
    defineField({
      name: "pageBuilder",
      title: fields.pageBuilder,
      type: "pageBuilder",
      description: descriptions.pageBuilder,
    }),
    defineField({
      name: "seo",
      title: fields.seo,
      type: "seoObject",
      description: descriptions.seo,
    }),
  ],
});
