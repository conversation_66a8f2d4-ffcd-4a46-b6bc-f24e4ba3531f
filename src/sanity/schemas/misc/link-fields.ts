import { defineField } from "sanity";
import { pageReferenceTypes } from "./page-reference-types";
import { linkFieldsDict } from "../../dictionary/studio/schemas/misc/link-fields";

const { fields } = linkFieldsDict;

export const linkFields = [
  defineField({
    title: fields.linkType,
    name: "linkType",
    type: "string",
    options: {
      list: [
        { title: fields.internal, value: "internal" },
        { title: fields.anchor, value: "anchor" },
        { title: fields.external, value: "external" },
        { title: fields.fileDownload, value: "fileDownload" },
        { title: fields.emailAddress, value: "emailAddress" },
      ],
    },
    initialValue: "internal",
  }),
  defineField({
    title: fields.linkAnchorLocation,
    name: "linkAnchorLocation",
    type: "string",
    options: {
      list: [
        { title: fields.currentPage, value: "currentPage" },
        { title: fields.choosePage, value: "choosePage" },
      ],
    },
    initialValue: "currentPage",
    hidden: ({ parent }) => parent?.linkType !== "anchor",
  }),
  defineField({
    name: "linkPageReference",
    title: fields.linkPageReference,
    type: "reference",
    to: [...pageReferenceTypes],
    hidden: ({ parent }) =>
      parent?.linkType !== "internal" &&
      !(
        parent?.linkType === "anchor" &&
        parent?.linkAnchorLocation === "choosePage"
      ),
  }),
  defineField({
    name: "linkAnchorId",
    title: fields.linkAnchorId,
    type: "string",
    hidden: ({ parent }) => parent?.linkType !== "anchor",
  }),
  defineField({
    name: "linkSearchParameters",
    title: fields.linkSearchParameters,
    type: "array",
    of: [
      {
        type: "object",
        fields: [
          defineField({
            name: "parameterType",
            title: "Parameter Type",
            type: "string",
            options: {
              list: [
                { title: "Simple Key-Value", value: "simple" },
                { title: "JSON Object", value: "json" },
                { title: "Raw Parameter String", value: "raw" },
              ],
            },
            initialValue: "simple",
          }),
          defineField({
            name: "key",
            title: "Key",
            type: "string",
            description: "Parameter name (not used for raw parameters)",
            hidden: ({ parent }) => parent?.parameterType === "raw",
          }),
          defineField({
            name: "value",
            title: "Value",
            type: "string",
            description: "Simple string value",
            hidden: ({ parent }) => parent?.parameterType !== "simple",
          }),
          defineField({
            name: "jsonValue",
            title: "JSON Value",
            type: "text",
            description:
              'JSON object that will be URL-encoded (e.g., {"priceTo":50000,"makeModelVersions":[{"makeKey":"audi"}]})',
            rows: 4,
            validation: (Rule) =>
              Rule.custom((value, context) => {
                const parent = context.parent as any;
                if (parent?.parameterType === "json" && value) {
                  try {
                    JSON.parse(value);
                    return true;
                  } catch (e) {
                    return "Invalid JSON format";
                  }
                }
                return true;
              }),
            hidden: ({ parent }) => parent?.parameterType !== "json",
          }),
          defineField({
            name: "rawValue",
            title: "Raw Parameter String",
            type: "text",
            description:
              "Complete parameter string (e.g., query=%7B%22priceTo%22%3A50000%7D&sort=price)",
            rows: 3,
            hidden: ({ parent }) => parent?.parameterType !== "raw",
          }),
        ],
        preview: {
          select: {
            parameterType: "parameterType",
            key: "key",
            value: "value",
            jsonValue: "jsonValue",
            rawValue: "rawValue",
          },
          prepare(selection) {
            const { parameterType, key, value, jsonValue, rawValue } =
              selection;

            switch (parameterType) {
              case "simple":
                const simpleEncoded =
                  key && value
                    ? `${encodeURIComponent(key)}=${encodeURIComponent(value)}`
                    : "";
                return {
                  title: key ? `${key}: ${value || ""}` : "Simple Parameter",
                  subtitle: simpleEncoded || "Simple Key-Value",
                };
              case "json":
                let jsonEncoded = "";
                if (key && jsonValue) {
                  try {
                    // Validate and encode the JSON
                    JSON.parse(jsonValue);
                    jsonEncoded = `${encodeURIComponent(key)}=${encodeURIComponent(jsonValue)}`;
                  } catch (e) {
                    jsonEncoded = "Invalid JSON";
                  }
                }
                return {
                  title: key ? `${key}: JSON Object` : "JSON Parameter",
                  subtitle: jsonEncoded || "JSON Object",
                };
              case "raw":
                return {
                  title: "Raw Parameters",
                  subtitle: rawValue || "Raw Parameter String",
                };
              default:
                return {
                  title: "Parameter",
                  subtitle: parameterType || "Unknown type",
                };
            }
          },
        },
      },
    ],
  }),
  defineField({
    name: "linkExternalUrl",
    title: fields.linkExternalUrl,
    type: "url",
    validation: (Rule) =>
      Rule.uri({ scheme: ["http", "https", "mailto", "tel"] }),
    hidden: ({ parent }) => parent?.linkType !== "external",
  }),
  defineField({
    name: "linkEmailAddress",
    title: fields.linkEmailAddress,
    type: "string",
    hidden: ({ parent }) => parent?.linkType !== "emailAddress",
  }),
  defineField({
    name: "linkFileUrl",
    title: fields.linkFileUrl,
    type: "file",
    hidden: ({ parent }) => parent?.linkType !== "fileDownload",
  }),
];
