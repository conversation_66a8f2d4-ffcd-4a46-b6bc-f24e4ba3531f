import { fieldGroupsDict } from "../../dictionary/studio/schemas/misc/field-groups";

const { fields } = fieldGroupsDict;

export const fieldGroups = [
  { name: "general", title: fields.general },
  { name: "content", title: fields.content },
  { name: "seo", title: fields.seo },
  { name: "settings", title: fields.settings },
  { name: "appearance", title: fields.appearance },
  { name: "navbar", title: fields.navbar },
  { name: "slideOutMenu", title: fields.slideOutMenu },
  { name: "footer", title: fields.footer },
  { name: "layout", title: fields.layout },
  { name: "mobileMenu", title: fields.mobileMenu },
  { name: "companyDetails", title: fields.companyDetails },
  // Center-specific groups
  { name: "addressContact", title: fields.addressContact },
  { name: "branding", title: fields.branding },
  { name: "services", title: fields.services },
  { name: "integration", title: fields.integration },
  // Brand & Automotive-specific groups
  { name: "identity", title: fields.identity },
  { name: "assets", title: fields.assets },
  { name: "marketing", title: fields.marketing },
  { name: "manufacturer", title: fields.manufacturer },
  { name: "technical", title: fields.technical },
  { name: "social", title: fields.social },
  { name: "display", title: fields.display },
  // Product/Vehicle-specific groups
  { name: "specifications", title: fields.specifications },
  { name: "features", title: fields.features },
  { name: "pricing", title: fields.pricing },
  { name: "availability", title: fields.availability },
  // Offer-specific groups
  { name: "conditions", title: fields.conditions },
  { name: "eligibility", title: fields.eligibility },
  { name: "promotion", title: fields.promotion },
  // New comprehensive offer groups
  { name: "targeting", title: fields.targeting },
  { name: "validity", title: fields.validity },
  { name: "financial", title: fields.financial },
  { name: "application", title: fields.application },
  { name: "compliance", title: fields.compliance },
  { name: "tracking", title: fields.tracking },
  { name: "workflow", title: fields.workflow },
  // B2B Solutions Pro specific groups
  { name: "businessTargeting", title: fields.businessTargeting },
  { name: "servicePortfolio", title: fields.servicePortfolio },
  { name: "fleetIntegration", title: fields.fleetIntegration },
  { name: "businessBenefits", title: fields.businessBenefits },
  { name: "keyContacts", title: fields.keyContacts },
  { name: "customerCases", title: fields.customerCases },
  { name: "b2bContent", title: fields.b2bContent },
];
