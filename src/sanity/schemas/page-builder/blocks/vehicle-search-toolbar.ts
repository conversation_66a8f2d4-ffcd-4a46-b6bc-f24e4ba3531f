import { defineType, defineField } from "sanity";
import { vehicleSearchToolbarDict } from "../../../dictionary/studio/schemas/page-builder/blocks/vehicle-search-toolbar";
import { Search } from "lucide-react";
import {
  FILTER_TYPES,
  LAYOUT_TYPES,
} from "../../../../lib/api/autoscout/const/filters";
import { MakeKeyInput } from "../../../components/make-key-input";
import { ModelKeyInput } from "../../../components/model-key-input";

const { fields, validation, descriptions } = vehicleSearchToolbarDict;

export const vehicleSearchToolbar = defineType({
  name: "vehicleSearchToolbar",
  title: fields.title,
  type: "object",
  icon: Search,
  groups: [
    { name: "filters", title: "Filters" },
    { name: "preview", title: "Preview" },
    { name: "behavior", title: "Behavior" },
  ],
  fields: [
    // Center configuration
    defineField({
      name: "enableCenterFilter",
      title: fields.enableCenterFilter,
      type: "boolean",
      group: "filters",
      description: descriptions.enableCenterFilter,
      initialValue: true,
    }),
    defineField({
      name: "defaultCenterId",
      title: fields.defaultCenterId,
      type: "number",
      group: "filters",
      description: descriptions.defaultCenterId,
      hidden: ({ parent }) => !parent?.enableCenterFilter,
    }),

    // Available filters - which filter cards to show (refactored like vehicle-list-block)
    defineField({
      name: "availableFilters",
      title: fields.availableFilters,
      type: "array",
      group: "filters",
      description: descriptions.availableFilters,
      of: [
        {
          type: "object",
          fields: [
            {
              name: "filterType",
              title: "Filter Type",
              type: "string",
              options: {
                list: [
                  {
                    title: fields.availableFilters_price,
                    value: FILTER_TYPES.PRICE,
                  },
                  {
                    title: fields.availableFilters_make,
                    value: FILTER_TYPES.MAKE,
                  },
                  {
                    title: fields.availableFilters_condition,
                    value: FILTER_TYPES.CONDITION,
                  },
                  {
                    title: fields.availableFilters_fuel,
                    value: FILTER_TYPES.FUEL,
                  },
                  {
                    title: fields.availableFilters_year,
                    value: FILTER_TYPES.YEAR,
                  },
                  {
                    title: fields.availableFilters_mileage,
                    value: FILTER_TYPES.MILEAGE,
                  },
                ],
              },
              validation: (rule) => rule.required(),
            },
          ],
          preview: {
            select: { filterType: "filterType" },
            prepare({ filterType }) {
              const filterLabels: Record<string, string> = {
                [FILTER_TYPES.PRICE]: fields.availableFilters_price,
                [FILTER_TYPES.MAKE]: fields.availableFilters_make,
                [FILTER_TYPES.CONDITION]: fields.availableFilters_condition,
                [FILTER_TYPES.FUEL]: fields.availableFilters_fuel,
                [FILTER_TYPES.YEAR]: fields.availableFilters_year,
                [FILTER_TYPES.MILEAGE]: fields.availableFilters_mileage,
              };
              return {
                title: filterLabels[filterType] || filterType,
              };
            },
          },
        },
      ],
      initialValue: [
        { filterType: FILTER_TYPES.MAKE },
        { filterType: FILTER_TYPES.PRICE },
        { filterType: FILTER_TYPES.CONDITION },
        { filterType: FILTER_TYPES.MILEAGE },
        { filterType: FILTER_TYPES.FUEL },
      ],
      validation: (rule) =>
        rule.min(1).error(validation.availableFiltersRequired),
    }),

    // Popular filter presets (refactored to use same approach as preFilters in vehicle-list-block)
    defineField({
      name: "popularFilters",
      title: fields.popularFilters,
      type: "array",
      group: "filters",
      description: descriptions.popularFilters,
      of: [
        {
          type: "object",
          fields: [
            {
              name: "label",
              type: "string",
              title: fields.presetLabel,
              validation: (rule) => rule.required(),
            },
            {
              name: "filters",
              type: "object",
              title: fields.presetFilters,
              fields: [
                {
                  name: "centerId",
                  type: "number",
                  title: fields.preFilters_centerId,
                },
                {
                  name: "makeKey",
                  type: "array",
                  of: [{ type: "string" }],
                  title: fields.preFilters_makeKey,
                  components: { input: MakeKeyInput },
                },
                {
                  name: "modelKey",
                  type: "array",
                  of: [{ type: "string" }],
                  title: fields.preFilters_modelKey,
                  components: { input: ModelKeyInput },
                },
                {
                  name: "fuelType",
                  type: "fuelTypeArray",
                  title: fields.preFilters_fuelType,
                },
                {
                  name: "conditionType",
                  type: "conditionTypeArray",
                  title: fields.preFilters_conditionType,
                },
                {
                  name: "priceFrom",
                  type: "number",
                  title: fields.preFilters_priceFrom,
                },
                {
                  name: "priceTo",
                  type: "number",
                  title: fields.preFilters_priceTo,
                },
              ],
            },
          ],
          preview: {
            select: { title: "label" },
          },
        },
      ],
    }),

    // Preview configuration
    defineField({
      name: "previewLayout",
      title: fields.previewLayout,
      type: "string",
      group: "preview",
      description: descriptions.previewLayout,
      options: {
        list: [
          {
            title: fields.previewLayout_carousel,
            value: LAYOUT_TYPES.CAROUSEL,
          },
          { title: fields.previewLayout_grid, value: LAYOUT_TYPES.GRID },
        ],
      },
      initialValue: LAYOUT_TYPES.CAROUSEL,
    }),
    defineField({
      name: "previewItemsPerView",
      title: fields.previewItemsPerView,
      type: "number",
      group: "preview",
      description: descriptions.previewItemsPerView,
      initialValue: 4,
      validation: (rule) => rule.min(1).max(8),
    }),

    // Behavior
    defineField({
      name: "targetRoute",
      title: fields.targetRoute,
      type: "string",
      group: "behavior",
      description: descriptions.targetRoute,
      initialValue: "/search",
      validation: (rule) =>
        rule.required().error(validation.targetRouteRequired),
    }),
    defineField({
      name: "showVehicleCount",
      title: fields.showVehicleCount,
      type: "boolean",
      group: "behavior",
      description: descriptions.showVehicleCount,
      initialValue: true,
    }),
    defineField({
      name: "autoUpdateCount",
      title: fields.autoUpdateCount,
      type: "boolean",
      group: "behavior",
      description: descriptions.autoUpdateCount,
      initialValue: true,
    }),
  ],
  preview: {
    select: {
      availableFilters: "availableFilters",
      popularFilters: "popularFilters",
    },
    prepare({
      availableFilters,
      popularFilters,
    }: {
      availableFilters?: any[];
      popularFilters?: any[];
    }) {
      const filterCount = availableFilters?.length || 0;
      const presetCount = popularFilters?.length || 0;
      return {
        title: "Vehicle Search Toolbar",
        subtitle: `${filterCount} filters • ${presetCount} presets`,
      };
    },
  },
});
