import type { SanityDocument } from "sanity";
import { Iframe } from "sanity-plugin-iframe-pane";
import type { DefaultDocumentNodeResolver } from "sanity/structure";

function getPreviewUrl(
  doc: SanityDocument & {
    slug?: { current: string };
    brand?: { slug: { current: string } };
  },
) {
  const url = `${(process.env.NEXT_PUBLIC_SITE_URL ?? process.env.VERCEL_URL) ? `https://${process.env.VERCEL_URL}` : "http://localhost:3000"}`;
  if (!doc?.slug?.current) {
    return url;
  }

  switch (doc._type) {
    case "post":
      return `${url}/blog/${doc.slug.current}`;
    case "project":
      return `${url}/projets/${doc.slug.current}`;
    case "service":
      return `${url}/services/${doc.slug.current}`;
    case "centersPage":
      return `${url}/centres`;
    case "center":
      return `${url}/centres/${doc.slug.current}`;
    case "brandsPage":
      return `${url}/gammes`;
    case "brand":
      return `${url}/gammes/${doc.slug.current}`;
    case "offersPage":
      return `${url}/offres`;
    case "offer":
      return `${url}/offres/${doc.slug.current}`;
    case "model":
      return `${url}/gammes/${doc.brand?.slug.current}/modeles/${doc.slug.current}`;
    default:
      return `${url}/${doc.slug.current}`;
  }
}

export const defaultDocumentNode: DefaultDocumentNodeResolver = (
  S,
  { schemaType },
) => {
  switch (schemaType) {
    case "page":
    case "post":
    case "project":
    case "service":
    case "centersPage":
    case "center":
    case "brandsPage":
    case "brand":
    case "model":
    case "offer":
    case "offersPage":
      return S.document().views([
        S.view.form(),
        S.view
          .component(Iframe)
          .options({
            url: {
              origin: "same-origin",
              preview: (doc: SanityDocument) => getPreviewUrl(doc),
              draftMode: "/api/draft-mode/enable",
            },
            reload: {
              button: true,
            },
            defaultWidth: "desktop",
            draftMode: {
              enable: "/api/draft-mode/enable",
            },
          })
          .title("Preview"),
      ]);
    default:
      return S.document().views([S.view.form()]);
  }
};
