import type { PresentationPluginOptions } from "sanity/presentation";
import { defineDocuments, defineLocations } from "sanity/presentation";

export const resolve: PresentationPluginOptions["resolve"] = {
  locations: {
    page: defineLocations({
      select: {
        title: "title",
        slug: "slug.current",
      },
      resolve: (doc) => ({
        locations: [
          {
            title: doc?.title || "Untitled",
            href: `/${doc?.slug}`,
          },
        ],
      }),
    }),
    post: defineLocations({
      select: {
        title: "title",
        slug: "slug.current",
      },
      resolve: (doc) => ({
        locations: [
          {
            title: doc?.title || "Untitled",
            href: `/blog/${doc?.slug}`,
          },
          { title: "Blog", href: `/blog` },
        ],
      }),
    }),
    service: defineLocations({
      select: {
        title: "title",
        slug: "slug.current",
      },
      resolve: (doc) => ({
        locations: [
          {
            title: doc?.title || "Untitled",
            href: `/services/${doc?.slug}`,
          },
          { title: "Services", href: `/services` },
        ],
      }),
    }),
    project: defineLocations({
      select: {
        title: "title",
        slug: "slug.current",
      },
      resolve: (doc) => ({
        locations: [
          {
            title: doc?.title || "Untitled",
            href: `/projects/${doc?.slug}`,
          },
          { title: "Projects Index", href: `/projects` },
        ],
      }),
    }),
    center: defineLocations({
      select: {
        title: "name",
        slug: "slug.current",
      },
      resolve: (doc) => ({
        locations: [
          {
            title: doc?.title || "Untitled",
            href: `/centres/${doc?.slug}`,
          },
          { title: "Centers Index", href: `/centres` },
        ],
      }),
    }),
    brand: defineLocations({
      select: {
        title: "name",
        slug: "slug.current",
      },
      resolve: (doc) => ({
        locations: [
          {
            title: doc?.title || "Untitled",
            href: `/gammes/${doc?.slug}`,
          },
          { title: "Brands Index", href: `/gammes` },
        ],
      }),
    }),
    vehicleModel: defineLocations({
      select: {
        title: "name",
        slug: "slug.current",
      },
      resolve: (doc) => ({
        locations: [
          {
            title: doc?.title || "Untitled",
            // @ts-expect-error: brand is a reference
            href: `/gammes/${doc?.brand?.slug.current}/${doc?.slug}`,
          },
          {
            title: "Models Index",
            // @ts-expect-error: brand is a reference
            href: `/gammes/${doc?.brand?.slug.current}/modeles`,
          },
        ],
      }),
    }),
    offer: defineLocations({
      select: {
        title: "name",
        slug: "slug.current",
      },
      resolve: (doc) => ({
        locations: [
          {
            title: doc?.title || "Untitled",
            href: `/offres/${doc?.slug}`,
          },
          { title: "Offers Index", href: `/offres` },
        ],
      }),
    }),
  },
  mainDocuments: defineDocuments([
    {
      route: "/",
      filter: `_type == 'page' && slug.current == 'home'`,
    },
    {
      route: "/:slug",
      filter: `_type == 'page' && slug.current == $slug`,
    },
    {
      route: "/projects/:slug",
      filter: `_type == 'project' && slug.current == $slug`,
    },
    {
      route: "/services/:slug",
      filter: `_type == 'service' && slug.current == $slug`,
    },
    {
      route: "/blog",
      type: "blogPage",
    },
    {
      route: "/blog/:slug",
      filter: `_type == 'post' && slug.current == $slug`,
    },
    {
      route: "/centres",
      type: "centersPage",
    },
    {
      route: "/centres/:slug",
      filter: `_type == 'center' && slug.current == $slug`,
    },
    {
      route: "/gammes",
      type: "brandsPage",
    },
    {
      route: "/gammes/:slug",
      filter: `_type == 'brand' && slug.current == $slug`,
    },
    {
      route: "/gammes/:brandSlug/modeles/:modelSlug",
      filter: `_type == 'vehicleModel' && slug.current == $modelSlug`,
    },
    {
      route: "/offres",
      type: "offersPage",
    },
    {
      route: "/offres/:slug",
      filter: `_type == 'offer' && slug.current == $slug`,
    },
  ]),
};
