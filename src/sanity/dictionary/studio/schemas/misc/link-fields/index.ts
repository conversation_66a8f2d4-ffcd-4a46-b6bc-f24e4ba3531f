import * as en from "./en";
import * as fr from "./fr";
import { setupSchemaTranslations } from "../../utils";

const { dict, getTranslations } = setupSchemaTranslations({
  en: en.linkFieldsTranslations,
  fr: fr.linkFieldsTranslations,
});

export const linkFieldsDict = dict;
export const getLinkFieldsTranslations = () => getTranslations();
export type LinkFieldsTranslations = typeof en.linkFieldsTranslations;
