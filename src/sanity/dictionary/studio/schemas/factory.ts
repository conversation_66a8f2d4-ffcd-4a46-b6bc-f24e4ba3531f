// Generic schema translation factory
export interface SchemaTranslations {
  document?: {
    name: string;
    title: string;
  };
  fields: Record<string, string>;
  validation: Record<string, string>;
  descriptions: Record<string, string>;
}

export interface SchemaTranslationDictionaries<T extends SchemaTranslations> {
  en: T;
  fr: T;

  [key: string]: T;
}

// Generic factory class for schema translations
export class SchemaTranslationFactory<T extends SchemaTranslations> {
  private dictionaries: SchemaTranslationDictionaries<T>;
  private currentLanguage: keyof SchemaTranslationDictionaries<T>;

  constructor(dictionaries: SchemaTranslationDictionaries<T>) {
    this.dictionaries = dictionaries;
    this.currentLanguage = this.getCurrentLanguage();
  }

  private getCurrentLanguage(): keyof SchemaTranslationDictionaries<T> {
    const envLang = process.env.NEXT_PUBLIC_DEFAULT_LANGUAGE;
    return envLang && envLang in this.dictionaries ? envLang : "en";
  }

  public getTranslations<K extends keyof SchemaTranslationDictionaries<T>>(
    language?: K,
  ) {
    const targetLanguage = language || this.currentLanguage;
    return this.dictionaries[targetLanguage] || this.dictionaries.en;
  }

  public getDict() {
    return this.getTranslations();
  }

  public createField(fieldName: string, options: Record<string, string> = {}) {
    const dict = this.getDict();
    return {
      name: fieldName,
      title: dict.fields[fieldName],
      description: dict.descriptions[fieldName],
      ...options,
    };
  }

  public getAvailableLanguages() {
    return Object.keys(
      this.dictionaries,
    ) as (keyof SchemaTranslationDictionaries<T>)[];
  }
}

// Utility function to create a schema translation factory
export function createSchemaTranslations<T extends SchemaTranslations>(
  dictionaries: SchemaTranslationDictionaries<T>,
) {
  return new SchemaTranslationFactory(dictionaries);
}
