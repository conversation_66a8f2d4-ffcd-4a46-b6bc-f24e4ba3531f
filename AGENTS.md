# AGENTS.md - Development Guidelines

## Build/Test/Lint Commands

- `pnpm dev` - Start development server with Turbopack
- `pnpm build` - Build for production
- `pnpm lint` - Run ESLint
- `pnpm lint:fix` - Fix ESLint issues automatically
- `pnpm lint:tsc` - TypeScript type checking
- `pnpm prettier` - Format code with Prettier
- No test framework configured - verify changes manually

## Code Style Guidelines

- **Imports**: Use `type` imports for types (`import type { ... }`), separate type imports
- **Formatting**: Prettier with default config, 2-space indentation
- **Types**: Strict TypeScript, prefer interfaces for props, use `type` for unions
- **Naming**: camelCase for variables/functions, PascalCase for components/types
- **Components**: Default exports for pages/components, named exports for utilities
- **Paths**: Use `@/` alias for src imports
- **Styling**: Tailwind CSS with `cn()` utility for conditional classes
- **Error Handling**: Use react-hot-toast for user notifications

## Architecture & Patterns

- **SOLID Principles**: 
  - Single Responsibility: Each component/hook has one clear purpose
  - Open/Closed: Extend functionality through composition, not modification
  - Liskov Substitution: Components can be replaced with subtypes
  - Interface Segregation: Use specific interfaces for specific needs
  - Dependency Inversion: Depend on abstractions, not concrete implementations

- **Component Structure**: 
  - Separate UI components from business logic
  - Use server components for data fetching, client components for interactivity
  - Implement container/presentation pattern where appropriate

- **API Integration**: 
  - Use generated AutoScout API types exclusively
  - No arbitrary types or duplication across the stack
  - Validate all API responses with Zod schemas

- **State Management**: 
  - React Query for server state with proper cache configuration
  - URL state management with next-typesafe-url for search parameters
  - Minimize local React state, derive from URL when possible
  - Use React Query's select option for data transformation

- **Validation**: 
  - Zod schemas for runtime validation
  - TypeScript for compile-time type safety
  - Proper error handling with fallbacks

- **Performance**: 
  - Implement lazy loading, memoization, and code splitting
  - Use useCallback/useMemo for expensive operations
  - Debounce user inputs for search/filter operations

## Component Composition

- **Hook Composition**: Compose complex hooks from smaller, focused hooks
- **Component Factories**: Use factory functions to create specialized components
- **Shared Layouts**: Reuse layout components with children/render props
- **Context Providers**: Use context for cross-component state when necessary
- **Prop Drilling Alternatives**: Use context or composition to avoid prop drilling

## Filter Component Architecture

- **URL State**: Store filter state in URL for shareable/bookmarkable searches
- **Reactive Updates**: Use next-typesafe-url for reactive URL state management
- **Composition**: Compose complex filters from smaller, focused components
- **Reusable Hooks**: Extract common filter logic into reusable hooks
- **Type Safety**: Use generated API types for filter parameters

## Sanity CMS Guidelines

- **Schema Translations**: Follow exact LLM.md pattern - create en.ts/fr.ts/index.ts for each schema
- **Translation Structure**: Use flat `fields` object, dot notation for nested fields
- **Export Naming**: `[schema]ObjectDict`, `create[Schema]ObjectField`, `get[Schema]ObjectTranslations`
- **No String Literals**: All UI labels must use translation keys from dictionary
- **Validation**: Run type/lint checks after each schema refactor

## UI/UX Standards

- **Design System**: Use shadcn/ui components consistently
- **Responsive**: Mobile-first approach with Tailwind breakpoints
- **Accessibility**: ARIA labels, keyboard navigation, semantic HTML
- **Loading States**: Implement proper loading/error states for all async operations
- **Animations**: Use Framer Motion for smooth transitions and interactions

## Project Structure

- Next.js 15 with App Router, React 19, TypeScript, Tailwind CSS
- Sanity CMS integration with tRPC for API layer
- Components in `/src/components/`, utilities in `/src/lib/`
- AutoScout API integration with type-safe client

## Reference Documentation

- See `/doc/llm/rules.md` for comprehensive development patterns and architectural decisions
- See `/doc/llm/` for implementation guidelines and checklists
- See `/doc/autoscout/` for API integration patterns
- See `/doc/autocorner/` for brand architecture and content structure
- See `src/sanity/dictionary/studio/schemas/LLM.md` for schema translation patterns
