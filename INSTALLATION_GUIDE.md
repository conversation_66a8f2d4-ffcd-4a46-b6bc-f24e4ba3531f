# 🔧 Fix Build Error & Install New Blocks

## Step 1: Install Dependencies

First, install the required dependencies for the new blocks:

```bash
# Navigate to your project directory
cd "C:\Users\<USER>\Devocracy Starter\site-engine"

# Install the new Embla Carousel plugins
npm install embla-carousel-autoplay@^8.5.1 embla-carousel-fade@^8.5.1

# Update Sanity versions to fix compatibility
npm install @sanity/types@^3.93.0 next-sanity@^9.15.1

# Clear cache and reinstall
rm -rf node_modules package-lock.json
npm install
```

## Step 2: Clear Next.js Cache

```bash
# Clear Next.js cache
rm -rf .next

# Try building again
npm run build
```

## Step 3: Test Basic Build

The build should work now with the existing blocks. I've temporarily commented out the new blocks to isolate the Sanity version issue.

## Step 4: Re-enable New Blocks (After Successful Build)

Once the build is working, gradually re-enable the new blocks:

### A. Uncomment the imports in `src/sanity/schemas/index.ts`:

```typescript
// New Advanced Blocks
import contentGridsBlock from "./page-builder/blocks/content-grids-block";
import processTimelinesBlock from "./page-builder/blocks/process-timelines-block";
import statisticsBlock from "./page-builder/blocks/statistics-block";
import carouselBlock from "./page-builder/blocks/carousel-block";

// And add them to pageBuilderSchema array:
const pageBuilderSchema = [
  // ... existing blocks
  contentGridsBlock,
  processTimelinesBlock,
  statisticsBlock,
  carouselBlock,
];

// Also uncomment the object imports:
import { contentItemObject } from "./objects/content-item";
import { filterConfigObject } from "./objects/filter-config";
import { timelineStepObject } from "./objects/timeline-step";
import { timelineConfigObject } from "./objects/timeline-config";
import { animationConfigObject } from "./objects/animation-config";
import { carouselConfigObject } from "./objects/carousel-config";
import { dataSourceObject } from "./objects/data-source";

// And add them to objectSchema array:
const objectSchema = [
  // ... existing objects
  contentItemObject,
  filterConfigObject,
  timelineStepObject,
  timelineConfigObject,
  animationConfigObject,
  carouselConfigObject,
  dataSourceObject,
];
```

### B. Uncomment the imports in `src/components/page-builder/index.tsx`:

```typescript
const ContentGridsBlock = dynamic(() => import("./blocks/content-grids-block"));
const ProcessTimelinesBlock = dynamic(
  () => import("./blocks/process-timelines-block"),
);
const StatisticsBlock = dynamic(() => import("./blocks/statistics-block"));
const CarouselBlock = dynamic(() => import("./blocks/carousel-block"));

// And add them to PB_BLOCKS:
const PB_BLOCKS = {
  // ... existing blocks
  contentGridsBlock: ContentGridsBlock,
  processTimelinesBlock: ProcessTimelinesBlock,
  statisticsBlock: StatisticsBlock,
  carouselBlock: CarouselBlock,
} as const;
```

## Step 5: Test Again

```bash
npm run build
```

## 🎯 What's Included

Once re-enabled, you'll have these new advanced blocks:

### ✨ Statistics Block

- Animated counters with scroll triggers
- Custom formatting (prefix, suffix, decimals)
- Number abbreviation (K, M, B)
- Responsive grid layouts
- Custom colors and icons

### ✨ Content Grids Block

- Real-time search functionality
- Category and tag filtering
- Sortable content (title, date, priority)
- Responsive grid layouts
- Load more functionality

### ✨ Process Timeline Block

- Horizontal and vertical layouts
- Interactive step expansion
- Progress tracking
- Status indicators (completed, in-progress, pending)
- Responsive design

### ✨ Interactive Carousel Block

- Embla Carousel integration
- Multiple content types (cards, images, videos)
- Autoplay with controls
- Responsive slides per view
- Navigation and dot indicators

## 🔍 Troubleshooting

If you still get the `_singletons.mjs` error:

1. **Check Node.js version**: Ensure you're using Node.js 18+
2. **Clear all caches**:
   ```bash
   rm -rf node_modules package-lock.json .next
   npm install
   ```
3. **Check for conflicting dependencies**: Look for version mismatches in package.json
4. **Try with --no-turbopack**: Update your dev script temporarily:
   ```json
   "dev": "next dev"
   ```

## 📞 Need Help?

If the issue persists, please share:

- The exact error message
- Your Node.js version (`node --version`)
- Whether the error occurs during `npm run build` or `npm run dev`

The new blocks are fully implemented and ready to use once the build issue is resolved!
