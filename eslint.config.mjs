import { defineConfig, globalIgnores } from "eslint/config";
import path from "node:path";
import { fileURLToPath } from "node:url";
import js from "@eslint/js";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const compat = new FlatCompat({
  baseDirectory: __dirname,
  recommendedConfig: js.configs.recommended,
  allConfig: js.configs.all,
});

export default defineConfig([
  globalIgnores([
    "src/lib/api/autoscout/types",
    "src/lib/api/autoscout/schemas",
    "src/sanity/**/*",
    "**/sanity.config.ts",
    "**/sanity.types.ts",
  ]),
  {
    extends: compat.extends("next/core-web-vitals", "next/typescript"),

    rules: {
      "@typescript-eslint/consistent-type-imports": [
        "error",
        {
          prefer: "type-imports",
          fixStyle: "separate-type-imports",
        },
      ],

      "@typescript-eslint/no-import-type-side-effects": "error",
    },
  },
]);
