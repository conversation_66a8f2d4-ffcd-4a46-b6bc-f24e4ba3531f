# AutoCorner Website - Comprehensive Translations Checklist (REFACTORED)

## Overview
This document catalogs all hardcoded text strings found in the frontend that need to be moved to Sanity CMS for proper internationalization support, following the established dictionary system architecture from `doc/llm/sanity/translations/overview.md`.

## Current Translation Status
✅ **Sanity Studio**: Fully translated (EN/FR) with comprehensive dictionary system
❌ **Frontend Components**: Contains extensive hardcoded strings that need to be moved to Sanity
❌ **Global Strings**: Need centralized management following dictionary pattern

---

## ⚠️ CRITICAL CATEGORIZATION

After analyzing the frontend code, strings fall into two categories:

### 1. **CTAs/LINKS** (Interactive Elements with `href`)
- **Use**: `button-fields.ts` or `link-fields.ts` 
- **Examples**: Brand quick actions, navigation items with URLs
- **Pattern**: Elements with `href` properties that navigate or perform actions

### 2. **SIMPLE LABELS** (Static Text/Messages)
- **Use**: Dictionary labels in `src/sanity/dictionary/studio/schemas/labels/`
- **Examples**: Status messages, alt text, filter headers, condition labels
- **Pattern**: Static text, error messages, UI labels, alt text

---

## 1. BRANDS PAGE SINGLETON UPDATES

### 1.1 Update Brands Page Dictionary (`src/sanity/dictionary/studio/schemas/singletons/pages/brands-page/`)
**Current Status**: Basic page structure exists, needs UI content fields
**Following**: Dictionary system with proper SchemaTranslations interface structure

#### Update English translations (`en.ts`) - Add to existing fields:
```typescript
export const brandsPageTranslations = {
  document: {
    name: "brandsPage",
    title: "Brands Page",
  },
  
  fields: {
    // Existing fields
    title: "Page Title",
    slug: "URL Slug", 
    pageBuilder: "Page Builder",
    seo: "SEO",
    
    // Brand Grid States Group (SIMPLE LABELS)
    brandGridStates: "Brand Grid States",
    noBrandsFound: "No Brands Found",
    checkBackLater: "Check back later for new brands.",
    noVehiclesFound: "No Vehicles Found",
    checkBackForModels: "Check back later for new models.",
    noBrandsAvailable: "No Brands Available",
    
    // Vehicle Model Content Group (SIMPLE LABELS)
    vehicleModelContent: "Vehicle Model Content",
    details: "DETAILS",
    interestedIn: "Interested in this",
    specialistsCanHelp: "specialists can help you configure, finance, and schedule a test drive.",
    testDrive: "Test Drive",
    contact: "Contact",
    logoAltText: "logo",
    imageAltText: "image",
    
    // Vehicle Model Tabs Group (SIMPLE LABELS)
    vehicleModelTabs: "Vehicle Model Tabs",
    overviewTab: "OVERVIEW",
    techSpecificationTab: "TECH SPECIFICATION",
    vehiclesTab: "VEHICLES",
    galleryTab: "GALLERY",
    offersTab: "OFFERS",
    configureTab: "CONFIGURE",
    technicalDetails: "Technical Details",
    availableModels: "Available Models",
    specialOffers: "Special Offers & Promotions",
  },
  
  validation: {},
  
  descriptions: {
    // Existing descriptions
    title: "Title of the Brands page",
    slug: "URL slug for the Brands page",
    pageBuilder: "Page content and layout configuration for Brands presentation",
    seo: "Search engine optimization settings for Brands page",
    
    // Group descriptions
    brandGridStates: "Messages for empty states and grid conditions",
    vehicleModelContent: "Content labels and messages for vehicle model pages",
    vehicleModelTabs: "Tab labels and descriptions for vehicle model sections",
    
    // Individual field descriptions
    noBrandsFound: "Message when no brands are found",
    checkBackLater: "Message to check back later for brands",
    noVehiclesFound: "Message when no vehicles are found",
    checkBackForModels: "Message to check back later for models",
    noBrandsAvailable: "Message when no brands are available",
    details: "Uppercase label for details section",
    interestedIn: "Label for interested in prefix text",
    specialistsCanHelp: "Label for specialists can help description text",
    testDrive: "Label for test drive action",
    contact: "Label for contact action",
    logoAltText: "Alt text pattern for logos",
    imageAltText: "Alt text pattern for images",
    overviewTab: "Uppercase label for overview tab",
    techSpecificationTab: "Uppercase label for technical specification tab",
    vehiclesTab: "Uppercase label for vehicles tab",
    galleryTab: "Uppercase label for gallery tab",
    offersTab: "Uppercase label for offers tab",
    configureTab: "Uppercase label for configure tab",
    technicalDetails: "Label for technical details description",
    availableModels: "Label for available models description",
    specialOffers: "Label for special offers description",
  },
} as const;
```

#### Update French translations (`fr.ts`) - Add to existing fields:
```typescript
export const brandsPageTranslations = {
  document: {
    name: "brandsPage",
    title: "Page des marques",
  },
  
  fields: {
    // Existing fields
    title: "Titre de la page",
    slug: "Identifiant URL",
    pageBuilder: "Constructeur de page",
    seo: "SEO",
    
    // Brand Grid States Group (SIMPLE LABELS)
    brandGridStates: "États de grille de marques",
    noBrandsFound: "Aucune marque trouvée",
    checkBackLater: "Revenez plus tard pour de nouvelles marques.",
    noVehiclesFound: "Aucun véhicule trouvé",
    checkBackForModels: "Revenez plus tard pour de nouveaux modèles.",
    noBrandsAvailable: "Aucune marque disponible",
    
    // Vehicle Model Content Group (SIMPLE LABELS)
    vehicleModelContent: "Contenu modèle de véhicule",
    details: "DÉTAILS",
    interestedIn: "Intéressé par ce",
    specialistsCanHelp: "spécialistes peuvent vous aider à configurer, financer et programmer un essai routier.",
    testDrive: "Essai routier",
    contact: "Contact",
    logoAltText: "logo",
    imageAltText: "image",
    
    // Vehicle Model Tabs Group (SIMPLE LABELS)
    vehicleModelTabs: "Onglets modèle de véhicule",
    overviewTab: "APERÇU",
    techSpecificationTab: "SPÉCIFICATIONS TECHNIQUES",
    vehiclesTab: "VÉHICULES",
    galleryTab: "GALERIE",
    offersTab: "OFFRES",
    configureTab: "CONFIGURER",
    technicalDetails: "Détails techniques",
    availableModels: "Modèles disponibles",
    specialOffers: "Offres spéciales et promotions",
  },
  
  validation: {},
  
  descriptions: {
    // Same descriptions as English (descriptions are typically in the default language)
    // ... (copy from English version)
  },
} as const;
```

### 1.2 Update Brands Page Schema (`src/sanity/schemas/singletons/pages/brands-page.ts`)
**Following**: Use dictionary fields with proper field groups

#### Add new field groups to field-groups.ts:
```typescript
// Add to src/sanity/schemas/misc/field-groups.ts
{ name: "brandSettings", title: fields.brandSettings },
```

#### Update schema to use dictionary fields:
```typescript
import { brandsPageDict } from "../../../dictionary/studio/schemas/singletons/pages/brands-page";

const { document, fields, descriptions } = brandsPageDict;

export default defineType({
  name: document.name,
  title: document.title,
  type: "document",
  fieldsets: [...fieldsets],
  groups: [...fieldGroups],
  fields: [
    // Existing fields...
    
    // Brand Grid States Object (SIMPLE LABELS)
    defineField({
      name: "brandGridStates",
      title: fields.brandGridStates,
      type: "object",
      group: "brandSettings",
      description: descriptions.brandGridStates,
      fields: [
        {
          name: "noBrandsFound",
          title: fields.noBrandsFound,
          type: "string",
          initialValue: fields.noBrandsFound,
          description: descriptions.noBrandsFound,
        },
        {
          name: "checkBackLater",
          title: fields.checkBackLater,
          type: "string",
          initialValue: fields.checkBackLater,
          description: descriptions.checkBackLater,
        },
        // ... continue for all brand grid state fields
      ],
    }),
    
    // Vehicle Model Content Object (SIMPLE LABELS)
    defineField({
      name: "vehicleModelContent",
      title: fields.vehicleModelContent,
      type: "object",
      group: "brandSettings",
      description: descriptions.vehicleModelContent,
      fields: [
        {
          name: "details",
          title: fields.details,
          type: "string",
          initialValue: fields.details,
          description: descriptions.details,
        },
        // ... continue for all vehicle model content fields
      ],
    }),
    
    // Vehicle Model Tabs Object (SIMPLE LABELS)
    defineField({
      name: "vehicleModelTabs",
      title: fields.vehicleModelTabs,
      type: "object",
      group: "brandSettings",
      description: descriptions.vehicleModelTabs,
      fields: [
        {
          name: "overviewTab",
          title: fields.overviewTab,
          type: "string",
          initialValue: fields.overviewTab,
          description: descriptions.overviewTab,
        },
        // ... continue for all vehicle model tab fields
      ],
    }),
  ],
});
```

---

## ⚠️ CTAs/LINKS TO HANDLE SEPARATELY

These are interactive elements with `href` properties that need `button-fields.ts` or `link-fields.ts`:

### Brand Quick Actions (from `brand-quick-actions.tsx`):
**Analysis**: These are Link components with dynamic `href` properties
**Solution**: Create configurable link objects in brands-page schema

```typescript
// Add to brands-page schema
defineField({
  name: "brandQuickActions",
  title: "Brand Quick Actions",
  type: "array",
  of: [
    {
      type: "object",
      fields: [
        {
          name: "label",
          title: "Action Label",
          type: "string",
        },
        {
          name: "description", 
          title: "Action Description",
          type: "string",
        },
        {
          name: "icon",
          title: "Icon",
          type: "lucideIcon",
        },
        ...linkFields, // Use existing link-fields.ts
      ],
    },
  ],
}),
```

**Hardcoded values to replace**:
- "Book Test Drive" → `/gammes/${brand?.slug}/test-drive`
- "Find Dealer" → `/gammes/${brand?.slug}/dealers`
- "Contact Sales" → `/gammes/${brand?.slug}/contact`
- "Get Brochure" → `/gammes/${brand?.slug}/brochure`
- "Experience the vehicle" (description)
- "Locate nearest dealer" (description)
- "Speak with expert" (description)
- "Download materials" (description)

### Vehicle Model Navigation (from `vehicle-model-navigation.tsx`):
**Analysis**: These are Link components with dynamic `href` properties
**Solution**: Create configurable navigation objects in brands-page schema

```typescript
// Add to brands-page schema
defineField({
  name: "vehicleModelNavigation",
  title: "Vehicle Model Navigation",
  type: "array",
  of: [
    {
      type: "object",
      fields: [
        {
          name: "label",
          title: "Navigation Label",
          type: "string",
        },
        {
          name: "description",
          title: "Navigation Description", 
          type: "string",
        },
        {
          name: "urlPath",
          title: "URL Path",
          type: "string",
          description: "Path to append to base URL (e.g., 'gallery', 'configure')",
        },
      ],
    },
  ],
}),
```

**Hardcoded values to replace**:
- "Overview" → `/gammes/${brandSlug}/modeles/${modelSlug}`
- "Gallery" → `/gammes/${brandSlug}/modeles/${modelSlug}/gallery`
- "Configure" → `/gammes/${brandSlug}/modeles/${modelSlug}/configure`
- "Compare" → `/gammes/${brandSlug}/modeles/${modelSlug}/compare`
- "Vehicle details" (description)
- "Photos & videos" (description)
- "Build & price" (description)
- "VS competitors" (description)

### Vehicle Model Quick Actions (from `vehicle-model-quick-actions.tsx`):
**Analysis**: These are Link components with dynamic `href` properties
**Solution**: Create configurable action objects in brands-page schema

```typescript
// Add to brands-page schema
defineField({
  name: "vehicleModelQuickActions",
  title: "Vehicle Model Quick Actions",
  type: "array",
  of: [
    {
      type: "object",
      fields: [
        {
          name: "label",
          title: "Action Label",
          type: "string",
        },
        {
          name: "description",
          title: "Action Description",
          type: "string",
        },
        {
          name: "icon",
          title: "Icon",
          type: "lucideIcon",
        },
        {
          name: "urlPath",
          title: "URL Path",
          type: "string",
          description: "Path to append to base URL (e.g., 'test-drive', 'configure')",
        },
      ],
    },
  ],
}),
```

**Hardcoded values to replace**:
- "Book Test Drive" → `/gammes/${brandSlug}/modeles/${modelSlug}/test-drive`
- "Configure" → `/gammes/${brandSlug}/modeles/${modelSlug}/configure`
- "Compare" → `/gammes/${brandSlug}/modeles/${modelSlug}/compare`
- "Find Dealer" → `/gammes/${brandSlug}/dealers`
- "Contact Sales" → `/gammes/${brandSlug}/contact`
- "Share Vehicle" → `#`
- "Experience this vehicle" (description)
- "Build & price" (description)
- "VS competitors" (description)
- "Locate nearest dealer" (description)
- "Speak with expert" (description)
- "Send to friend" (description)

---

## 2. VEHICLE SETTINGS LABELS (NEW)

### 2.1 Create Vehicle Settings Dictionary (`src/sanity/dictionary/studio/schemas/labels/vehicle-settings/`)
**Purpose**: Centralize all vehicle-related UI labels and messages
**Following**: Dictionary system with proper SchemaTranslations interface structure

#### Create English translations (`en.ts`):
```typescript
export const vehicleSettingsTranslations = {
  document: {
    name: "vehicleSettings",
    title: "Vehicle Settings",
  },

  fields: {
    // Vehicle Specifications Group
    vehicleSpecifications: "Vehicle Specifications",
    fuelType: "Fuel Type",
    transmission: "Transmission",
    power: "Power",
    mileage: "Mileage",
    year: "Year",
    condition: "Condition",

    // Condition Labels Group
    conditionLabels: "Condition Labels",
    new: "New",
    used: "Used",
    demonstration: "Demonstration",
    oldtimer: "Oldtimer",
    preRegistered: "Pre-registered",

    // Fuel Type Labels Group
    fuelTypeLabels: "Fuel Type Labels",
    petrol: "Petrol",
    diesel: "Diesel",
    electric: "Electric",
    hevPetrol: "Hybrid Petrol",
    hevDiesel: "Hybrid Diesel",
    phevPetrol: "Plug-in Hybrid Petrol",
    phevDiesel: "Plug-in Hybrid Diesel",
    cngPetrol: "CNG/Petrol",
    lpgPetrol: "LPG/Petrol",
    hydrogen: "Hydrogen",
    ethanolPetrol: "Ethanol",
    mhevDiesel: "Micro-hybrid Diesel",
    mhevPetrol: "Micro-hybrid Petrol",
    twoStrokeMixture: "Two-stroke Engine",

    // Fuel Type Groups
    fuelTypeGroups: "Fuel Type Groups",
    petrolGroup: "Petrol",
    dieselGroup: "Diesel",
    electricGroup: "Electric",
    hybridGroup: "Hybrid",
    otherGroup: "Other",

    // Vehicle Filter Labels Group
    vehicleFilterLabels: "Vehicle Filter Labels",
    filters: "Filters",
    clearAll: "Clear All",
    removeFilter: "Remove Filter",
    showResults: "Show Results",
    viewResults: "View Results",
    loading: "Loading...",
    vehiclesAvailable: "vehicles available",
    noVehiclesFound: "No Vehicles Found",

    // Vehicle Search Labels Group
    vehicleSearchLabels: "Vehicle Search Labels",
    searchVehicles: "Search Vehicles",
    searchPlaceholder: "Search...",
    clearSearch: "Clear Search",
    noResultsFound: "No Results Found",
  },

  validation: {},

  descriptions: {
    vehicleSpecifications: "Labels for vehicle specification fields",
    conditionLabels: "Labels for different vehicle conditions",
    fuelTypeLabels: "Labels for all fuel types",
    fuelTypeGroups: "Group labels for fuel type categories",
    vehicleFilterLabels: "Labels for vehicle filtering interface",
    vehicleSearchLabels: "Labels for vehicle search functionality",
    fuelType: "Label for fuel type specification",
    transmission: "Label for transmission specification",
    power: "Label for power specification",
    mileage: "Label for mileage specification",
    year: "Label for year specification",
    condition: "Label for condition specification",
    new: "Label for new condition",
    used: "Label for used condition",
    demonstration: "Label for demonstration condition",
    oldtimer: "Label for oldtimer condition",
    preRegistered: "Label for pre-registered condition",
    petrol: "Label for petrol fuel type",
    diesel: "Label for diesel fuel type",
    electric: "Label for electric fuel type",
    hevPetrol: "Label for hybrid petrol fuel type",
    hevDiesel: "Label for hybrid diesel fuel type",
    phevPetrol: "Label for plug-in hybrid petrol fuel type",
    phevDiesel: "Label for plug-in hybrid diesel fuel type",
    cngPetrol: "Label for CNG/Petrol fuel type",
    lpgPetrol: "Label for LPG/Petrol fuel type",
    hydrogen: "Label for hydrogen fuel type",
    ethanolPetrol: "Label for ethanol fuel type",
    mhevDiesel: "Label for micro-hybrid diesel fuel type",
    mhevPetrol: "Label for micro-hybrid petrol fuel type",
    twoStrokeMixture: "Label for two-stroke engine fuel type",
    petrolGroup: "Group label for petrol fuel types",
    dieselGroup: "Group label for diesel fuel types",
    electricGroup: "Group label for electric fuel types",
    hybridGroup: "Group label for hybrid fuel types",
    otherGroup: "Group label for other fuel types",
    filters: "Label for filters header",
    clearAll: "Label for clear all filters action",
    removeFilter: "Label for remove filter action",
    showResults: "Label for show results action",
    viewResults: "Label for view results action",
    loading: "Label for loading state",
    vehiclesAvailable: "Label for vehicles available count",
    noVehiclesFound: "Message when no vehicles are found",
    searchVehicles: "Label for search vehicles action",
    searchPlaceholder: "Placeholder text for search input",
    clearSearch: "Label for clear search action",
    noResultsFound: "Message when no search results are found",
  },
} as const;
```

#### Create French translations (`fr.ts`):
```typescript
export const vehicleSettingsTranslations = {
  document: {
    name: "vehicleSettings",
    title: "Paramètres de véhicule",
  },

  fields: {
    // Vehicle Specifications Group
    vehicleSpecifications: "Spécifications du véhicule",
    fuelType: "Carburant",
    transmission: "Transmission",
    power: "Puissance",
    mileage: "Kilométrage",
    year: "Année",
    condition: "État",

    // Condition Labels Group
    conditionLabels: "Étiquettes d'état",
    new: "Neuf",
    used: "Occasion",
    demonstration: "Démonstration",
    oldtimer: "Oldtimer",
    preRegistered: "Pré-immatriculé",

    // Fuel Type Labels Group
    fuelTypeLabels: "Étiquettes de type de carburant",
    petrol: "Essence",
    diesel: "Diesel",
    electric: "Électrique",
    hevPetrol: "Hybride essence",
    hevDiesel: "Hybride diesel",
    phevPetrol: "Hybride rechargeable essence",
    phevDiesel: "Hybride rechargeable diesel",
    cngPetrol: "GNC/Essence",
    lpgPetrol: "GPL/Essence",
    hydrogen: "Hydrogène",
    ethanolPetrol: "Éthanol",
    mhevDiesel: "Micro-hybride diesel",
    mhevPetrol: "Micro-hybride essence",
    twoStrokeMixture: "Moteur à deux temps",

    // Fuel Type Groups
    fuelTypeGroups: "Groupes de types de carburant",
    petrolGroup: "Essence",
    dieselGroup: "Diesel",
    electricGroup: "Électrique",
    hybridGroup: "Hybride",
    otherGroup: "Autre",

    // Vehicle Filter Labels Group
    vehicleFilterLabels: "Étiquettes de filtre de véhicule",
    filters: "Filtres",
    clearAll: "Effacer tout",
    removeFilter: "Supprimer le filtre",
    showResults: "Voir les résultats",
    viewResults: "Voir les résultats",
    loading: "Chargement...",
    vehiclesAvailable: "véhicules disponibles",
    noVehiclesFound: "Aucun véhicule trouvé",

    // Vehicle Search Labels Group
    vehicleSearchLabels: "Étiquettes de recherche de véhicule",
    searchVehicles: "Rechercher des véhicules",
    searchPlaceholder: "Rechercher...",
    clearSearch: "Effacer la recherche",
    noResultsFound: "Aucun résultat trouvé",
  },

  validation: {},

  descriptions: {
    // Same descriptions as English (descriptions are typically in the default language)
    // ... (copy from English version)
  },
} as const;
```

### 2.2 Create Vehicle Settings Schema (`src/sanity/schemas/labels/vehicle-settings.ts`)
**Following**: Use dictionary fields with proper field groups

#### Add new field groups to field-groups.ts:
```typescript
// Add to src/sanity/schemas/misc/field-groups.ts
{ name: "vehicleSpecs", title: fields.vehicleSpecs },
{ name: "conditionLabels", title: fields.conditionLabels },
{ name: "fuelTypeLabels", title: fields.fuelTypeLabels },
{ name: "fuelTypeGroups", title: fields.fuelTypeGroups },
{ name: "vehicleFilterLabels", title: fields.vehicleFilterLabels },
{ name: "vehicleSearchLabels", title: fields.vehicleSearchLabels },
```

---

## 3. GLOBAL TRANSLATIONS LABELS (NEW)

### 3.1 Create Global Translations Dictionary (`src/sanity/dictionary/studio/schemas/labels/global-translations/`)
**Purpose**: Centralize all global UI strings that don't belong to specific documents
**Following**: Dictionary system with proper SchemaTranslations interface structure

#### Create English translations (`en.ts`):
```typescript
export const globalTranslationsTranslations = {
  document: {
    name: "globalTranslations",
    title: "Global Translations",
  },

  fields: {
    // Navigation & Layout Group
    navigation: "Navigation",
    menu: "Menu",
    home: "Home",
    back: "Back",
    close: "Close",
    noChildPagesYet: "No child pages yet",

    // Common Actions Group
    commonActions: "Common Actions",
    search: "Search",
    filter: "Filter",
    sort: "Sort",
    clear: "Clear",
    apply: "Apply",
    cancel: "Cancel",
    save: "Save",
    edit: "Edit",
    delete: "Delete",
    view: "View",
    readMore: "Read More",
    learnMore: "Learn More",

    // Status Messages & Error Handling Group
    statusMessages: "Status Messages",
    loading: "Loading...",
    error: "Error",
    success: "Success",
    noResults: "No Results",
    tryAgain: "Try Again",
    retry: "Retry",
    errorLoadingVehicles: "Error Loading Vehicles",
    unexpectedError: "An unexpected error occurred while loading vehicles. Please try again.",
    noHomepageSet: "No Homepage Set...",

    // Form Validation Group
    formValidation: "Form Validation",
    required: "is required",
    invalidEmail: "Invalid email address",
    submitError: "Submit Error",
    submitSuccess: "Submit Success",

    // Development/Demo Group
    development: "Development",
    siteEngine: "SiteEngine",
    runCommand: "Run the command to install demo content",
    disableDraftMode: "Disable Draft Mode",
    defaultMetaDescription: "Open-Source Next.js & Sanity Marketing Website Template.",

    // Centers Group
    centers: "Centers",
    noCentersFound: "No Centers Found",
    forType: "for type",

    // Shared Components Group
    sharedComponents: "Shared Components",
    goBackToPrevious: "Go back to previous page or anchor.",
    breadcrumbHome: "Home",
  },

  validation: {},

  descriptions: {
    navigation: "Navigation labels and menu items",
    commonActions: "Common action buttons and links",
    statusMessages: "Status messages and error handling",
    formValidation: "Form validation messages",
    development: "Development and demo related strings",
    centers: "Center-related messages",
    sharedComponents: "Shared component labels and messages",
    menu: "Label for menu button",
    home: "Label for home navigation",
    back: "Label for back button",
    close: "Label for close button",
    noChildPagesYet: "Message when no child pages exist",
    search: "Label for search action",
    filter: "Label for filter action",
    sort: "Label for sort action",
    clear: "Label for clear action",
    apply: "Label for apply action",
    cancel: "Label for cancel action",
    save: "Label for save action",
    edit: "Label for edit action",
    delete: "Label for delete action",
    view: "Label for view action",
    readMore: "Label for read more action",
    learnMore: "Label for learn more action",
    loading: "Label for loading state",
    error: "Label for error state",
    success: "Label for success state",
    noResults: "Message when no results are found",
    tryAgain: "Label for try again action",
    retry: "Label for retry action",
    errorLoadingVehicles: "Error message for vehicle loading failure",
    unexpectedError: "Generic unexpected error message",
    noHomepageSet: "Message when no homepage is configured",
    required: "Validation message for required fields",
    invalidEmail: "Validation message for invalid email",
    submitError: "Message for form submission error",
    submitSuccess: "Message for form submission success",
    siteEngine: "Label for site engine",
    runCommand: "Instruction for running demo installation command",
    disableDraftMode: "Label for disable draft mode action",
    defaultMetaDescription: "Default meta description for pages",
    noCentersFound: "Message when no centers are found",
    forType: "Preposition for center type filtering",
    goBackToPrevious: "Aria label for back button",
    breadcrumbHome: "Aria label for breadcrumb home link",
  },
} as const;
```

#### Create French translations (`fr.ts`):
```typescript
export const globalTranslationsTranslations = {
  document: {
    name: "globalTranslations",
    title: "Traductions globales",
  },

  fields: {
    // Navigation & Layout Group
    navigation: "Navigation",
    menu: "Menu",
    home: "Accueil",
    back: "Retour",
    close: "Fermer",
    noChildPagesYet: "Aucune page enfant pour le moment",

    // Common Actions Group
    commonActions: "Actions communes",
    search: "Rechercher",
    filter: "Filtrer",
    sort: "Trier",
    clear: "Effacer",
    apply: "Appliquer",
    cancel: "Annuler",
    save: "Enregistrer",
    edit: "Modifier",
    delete: "Supprimer",
    view: "Voir",
    readMore: "Lire plus",
    learnMore: "En savoir plus",

    // Status Messages & Error Handling Group
    statusMessages: "Messages de statut",
    loading: "Chargement...",
    error: "Erreur",
    success: "Succès",
    noResults: "Aucun résultat",
    tryAgain: "Réessayer",
    retry: "Réessayer",
    errorLoadingVehicles: "Erreur lors du chargement des véhicules",
    unexpectedError: "Une erreur inattendue s'est produite lors du chargement des véhicules. Veuillez réessayer.",
    noHomepageSet: "Aucune page d'accueil définie...",

    // Form Validation Group
    formValidation: "Validation de formulaire",
    required: "est requis",
    invalidEmail: "Adresse e-mail invalide",
    submitError: "Erreur de soumission",
    submitSuccess: "Soumission réussie",

    // Development/Demo Group
    development: "Développement",
    siteEngine: "SiteEngine",
    runCommand: "Exécuter la commande pour installer le contenu de démonstration",
    disableDraftMode: "Désactiver le mode brouillon",
    defaultMetaDescription: "Modèle de site web marketing Next.js et Sanity open-source.",

    // Centers Group
    centers: "Centres",
    noCentersFound: "Aucun centre trouvé",
    forType: "pour le type",

    // Shared Components Group
    sharedComponents: "Composants partagés",
    goBackToPrevious: "Retourner à la page précédente ou ancre.",
    breadcrumbHome: "Accueil",
  },

  validation: {},

  descriptions: {
    // Same descriptions as English (descriptions are typically in the default language)
    // ... (copy from English version)
  },
} as const;
```

### 3.2 Create Global Translations Schema (`src/sanity/schemas/labels/global-translations.ts`)
**Following**: Use dictionary fields with proper field groups

#### Add new field groups to field-groups.ts:
```typescript
// Add to src/sanity/schemas/misc/field-groups.ts
{ name: "navigation", title: fields.navigation },
{ name: "commonActions", title: fields.commonActions },
{ name: "statusMessages", title: fields.statusMessages },
{ name: "formValidation", title: fields.formValidation },
{ name: "development", title: fields.development },
{ name: "centers", title: fields.centers },
{ name: "sharedComponents", title: fields.sharedComponents },
```

---

## 4. COMPREHENSIVE HARDCODED STRINGS INVENTORY

### 4.1 Brands Directory - ALL FINDINGS
**Files with hardcoded strings**:

#### `src/app/(frontend)/gammes/_components/brand-grid.tsx`
**Category**: SIMPLE LABELS
- "No brands found" → brandGridStates.noBrandsFound
- "Check back later for new brands." → brandGridStates.checkBackLater

#### `src/app/(frontend)/gammes/_components/brand-card.tsx`
**Category**: SIMPLE LABELS
- Alt text generation: `${name} logo` → vehicleModelContent.logoAltText
- Fallback character display: `${name?.charAt(0) || "?"}` → (keep as is, dynamic)

#### `src/app/(frontend)/gammes/[brandSlug]/_components/brand-quick-actions.tsx`
**Category**: CTAs/LINKS (need button-fields or link-fields)
- "Book Test Drive" → `/gammes/${brand?.slug}/test-drive`
- "Find Dealer" → `/gammes/${brand?.slug}/dealers`
- "Contact Sales" → `/gammes/${brand?.slug}/contact`
- "Get Brochure" → `/gammes/${brand?.slug}/brochure`
- "Experience the vehicle" → (description)
- "Locate nearest dealer" → (description)
- "Speak with expert" → (description)
- "Download materials" → (description)

#### `src/app/(frontend)/gammes/[brandSlug]/modeles/[modelSlug]/_components/vehicle-model-navigation.tsx`
**Category**: CTAs/LINKS (need button-fields or link-fields)
- "Overview" → `/gammes/${brandSlug}/modeles/${modelSlug}`
- "Gallery" → `/gammes/${brandSlug}/modeles/${modelSlug}/gallery`
- "Configure" → `/gammes/${brandSlug}/modeles/${modelSlug}/configure`
- "Compare" → `/gammes/${brandSlug}/modeles/${modelSlug}/compare`
- "Vehicle details" → (description)
- "Photos & videos" → (description)
- "Build & price" → (description)
- "VS competitors" → (description)

#### `src/app/(frontend)/gammes/[brandSlug]/modeles/[modelSlug]/_components/vehicle-model-tabs.tsx`
**Category**: SIMPLE LABELS
- "OVERVIEW" → vehicleModelTabs.overviewTab
- "TECH SPECIFICATION" → vehicleModelTabs.techSpecificationTab
- "VEHICLES" → vehicleModelTabs.vehiclesTab
- "GALLERY" → vehicleModelTabs.galleryTab
- "OFFERS" → vehicleModelTabs.offersTab
- "CONFIGURE" → vehicleModelTabs.configureTab
- "Vehicle details" → vehicleModelTabs.technicalDetails
- "Technical details" → vehicleModelTabs.technicalDetails
- "Available models" → vehicleModelTabs.availableModels
- "Photos & videos" → vehicleModelTabs.photosAndVideos
- "Special offers & promotions" → vehicleModelTabs.specialOffers
- "Build & price" → vehicleModelTabs.buildAndPrice

#### `src/app/(frontend)/gammes/[brandSlug]/modeles/[modelSlug]/_components/vehicle-model-quick-actions.tsx`
**Category**: CTAs/LINKS (need button-fields or link-fields)
- "Book Test Drive" → `/gammes/${brandSlug}/modeles/${modelSlug}/test-drive`
- "Configure" → `/gammes/${brandSlug}/modeles/${modelSlug}/configure`
- "Compare" → `/gammes/${brandSlug}/modeles/${modelSlug}/compare`
- "Find Dealer" → `/gammes/${brandSlug}/dealers`
- "Contact Sales" → `/gammes/${brandSlug}/contact`
- "Share Vehicle" → `#`
- "Experience this vehicle" → (description)
- "Build & price" → (description)
- "VS competitors" → (description)
- "Locate nearest dealer" → (description)
- "Speak with expert" → (description)
- "Send to friend" → (description)

#### `src/app/(frontend)/gammes/[brandSlug]/modeles/[modelSlug]/_components/vehicle-model-overview.tsx`
**Category**: SIMPLE LABELS
- "OVERVIEW" → vehicleModelContent.overview
- "DETAILS" → vehicleModelContent.details
- Alt text patterns: `${name} logo` → vehicleModelContent.logoAltText
- Alt text patterns: `${name} image` → vehicleModelContent.imageAltText

#### `src/components/brands/models/grid/vehicle-model-grid.tsx`
**Category**: SIMPLE LABELS
- "No vehicles found" → brandGridStates.noVehiclesFound
- "Check back later for new models." → brandGridStates.checkBackForModels

#### `src/components/brands/models/card/vehicle-model-card.tsx`
**Category**: SIMPLE LABELS
- Alt text: `${brand?.name} logo` → vehicleModelContent.logoAltText
- "Back to Models" → (URL parameter, keep as is)

### 4.2 Vehicle Components - ALL FINDINGS
**Files with hardcoded strings**:

#### `src/components/vehicles/card/constants.ts`
**Category**: SIMPLE LABELS
- "Carburant" → vehicleSpecifications.fuelType
- "Transmission" → vehicleSpecifications.transmission
- "Puissance" → vehicleSpecifications.power
- "Kilométrage" → vehicleSpecifications.mileage
- "Neuf" → conditionLabels.new
- "Occasion" → conditionLabels.used
- "Démonstration" → conditionLabels.demonstration
- "Oldtimer" → conditionLabels.oldtimer
- "Pré-immatriculé" → conditionLabels.preRegistered

#### `src/components/vehicles/filters/components/FuelTypePopoverContent.tsx`
**Category**: SIMPLE LABELS
- "Essence" → fuelTypeLabels.petrol
- "Diesel" → fuelTypeLabels.diesel
- "Électrique" → fuelTypeLabels.electric
- "Hybride essence" → fuelTypeLabels.hevPetrol
- "Hybride diesel" → fuelTypeLabels.hevDiesel
- "Hybride rechargeable essence" → fuelTypeLabels.phevPetrol
- "Hybride rechargeable diesel" → fuelTypeLabels.phevDiesel
- "GNC/Essence" → fuelTypeLabels.cngPetrol
- "GPL/Essence" → fuelTypeLabels.lpgPetrol
- "Hydrogène" → fuelTypeLabels.hydrogen
- "Éthanol" → fuelTypeLabels.ethanolPetrol
- "Micro-hybride diesel" → fuelTypeLabels.mhevDiesel
- "Micro-hybride essence" → fuelTypeLabels.mhevPetrol
- "Moteur à deux temps" → fuelTypeLabels.twoStrokeMixture
- Group labels: "Essence" → fuelTypeGroups.petrolGroup, "Diesel" → fuelTypeGroups.dieselGroup, "Électrique" → fuelTypeGroups.electricGroup, "Hybride" → fuelTypeGroups.hybridGroup, "Autre" → fuelTypeGroups.otherGroup

#### `src/components/vehicles/filters/components/FilterHeader.tsx`
**Category**: SIMPLE LABELS
- "Filters" → vehicleFilterLabels.filters
- "Effacer" → vehicleFilterLabels.clearAll

#### `src/components/vehicles/filters/components/FilterChips.tsx`
**Category**: SIMPLE LABELS
- "Remove ${filter.displayKey} filter" → vehicleFilterLabels.removeFilter (aria-label)

#### `src/components/vehicles/toolbar/components/vehicle-count-button.tsx`
**Category**: SIMPLE LABELS
- "Voir les résultats" → vehicleFilterLabels.showResults
- "Chargement..." → vehicleFilterLabels.loading
- "${count} véhicules disponibles" → vehicleFilterLabels.vehiclesAvailable

#### `src/components/vehicles/blocks/states/vehicle-display-error.tsx`
**Category**: SIMPLE LABELS
- "Error Loading Vehicles" → statusMessages.errorLoadingVehicles
- "An unexpected error occurred while loading vehicles. Please try again." → statusMessages.unexpectedError
- "Retry" → statusMessages.retry

### 4.3 Navigation Components - ALL FINDINGS
**Files with hardcoded strings**:

#### `src/components/global/navigation/navigation-items.tsx`
**Category**: SIMPLE LABELS
- "No child pages yet" → navigation.noChildPagesYet

#### `src/components/global/navigation/brand-grid-loader.tsx`
**Category**: SIMPLE LABELS
- "No brands available" → brandGridStates.noBrandsAvailable

### 4.4 Centers Components - ALL FINDINGS
**Files with hardcoded strings**:

#### `src/components/centers/centers-grid.tsx`
**Category**: SIMPLE LABELS
- "Aucun centre trouvé" → centers.noCentersFound
- "pour le type" → centers.forType

### 4.5 Shared Components - ALL FINDINGS
**Files with hardcoded strings**:

#### `src/components/shared/install-demo-button.tsx`
**Category**: SIMPLE LABELS
- "SiteEngine" → development.siteEngine
- "Run the command to install demo content" → development.runCommand
- Command text: "npx sanity dataset import demo-content.tar.gz production" → (keep as is, technical)

#### `src/components/shared/disable-draft-mode.tsx`
**Category**: SIMPLE LABELS
- "Disable Draft Mode" → development.disableDraftMode

#### `src/components/shared/form.tsx`
**Category**: SIMPLE LABELS
- "is required" → formValidation.required
- "Invalid email address" → formValidation.invalidEmail

#### `src/components/shared/back-button.tsx`
**Category**: SIMPLE LABELS
- "Back" → navigation.back
- "Go back to previous page or anchor." → sharedComponents.goBackToPrevious (aria-label)

#### `src/components/shared/breadcrumbs.tsx`
**Category**: SIMPLE LABELS
- "Home" → sharedComponents.breadcrumbHome (aria-label)

### 4.6 Layout & Page Components - ALL FINDINGS
**Files with hardcoded strings**:

#### `src/app/(frontend)/layout.tsx`
**Category**: SIMPLE LABELS
- "Open-Source Next.js & Sanity Marketing Website Template." → development.defaultMetaDescription

#### `src/app/(frontend)/page.tsx`
**Category**: SIMPLE LABELS
- "No Homepage Set..." → statusMessages.noHomepageSet

#### `src/app/(frontend)/vehicles/[id]/_components/vehicle-title.tsx`
**Category**: SIMPLE LABELS + CONFIGURATION
- Locale formatting: `vehicle.mileage.toLocaleString("fr-CH")` → (make locale configurable)
- Unit display: " km", " • " → (keep as is, units)

---

## 5. IMPLEMENTATION PLAN (DICTIONARY SYSTEM)

### Phase 1: Update Field Groups (30 minutes)
1. **Update field-groups.ts** - Add all new translation-specific groups
2. **Update field-groups dictionary** - Add translations for new group names

### Phase 2: Update Brands Page Dictionary (1-2 hours)
1. **Update brands-page/en.ts** - Add all new fields following flat structure
2. **Update brands-page/fr.ts** - Add all French translations
3. **Update brands-page schema** - Use dictionary fields with proper groups
4. **Handle CTAs/Links separately** - Create configurable link/button objects

### Phase 3: Create Vehicle Settings Labels (2-3 hours)
1. **Create vehicle-settings dictionary** - All vehicle-related translations
2. **Create vehicle-settings schema** - In `src/sanity/schemas/labels/`
3. **Add to schema index** - Register new label schema

### Phase 4: Create Global Translations Labels (2-3 hours)
1. **Create global-translations dictionary** - All global UI strings
2. **Create global-translations schema** - In `src/sanity/schemas/labels/`
3. **Add to schema index** - Register new label schema

### Phase 5: Update Frontend Components (5-6 days)
1. **Create translation hooks** - Following existing pattern
2. **Update 30+ components** - Replace all hardcoded strings with CMS data
3. **Server-side data fetching** - Pass translation data as props
4. **Update locale formatting** - Make locale configurable
5. **Handle CTAs/Links** - Update components to use configurable link objects

### Phase 6: Testing & Validation (2-3 days)
1. **Test all languages** - Verify EN/FR switching works across all components
2. **Test default values** - Ensure French defaults display correctly
3. **Performance check** - Verify no performance regression
4. **Accessibility check** - Ensure aria-labels are properly translated

---

## 6. TECHNICAL NOTES (DICTIONARY SYSTEM)

### 6.1 Key Principles
- **Flat Structure**: All translations in `fields: Record<string, string>`
- **Proper Descriptions**: Use `descriptions: Record<string, string>` for help text
- **Labels Separation**: Use `src/sanity/schemas/labels/` for UI labels (separate from CMS content)
- **Dictionary Location**: Use `src/sanity/dictionary/studio/schemas/labels/` for label dictionaries
- **Field Groups**: Add new groups to `field-groups.ts` and incorporate in schemas
- **CTAs vs Labels**: Distinguish between interactive elements (CTAs/Links) and static labels

### 6.2 Query Strategy
- Use server-side data fetching (following user preference)
- Pass translation data as props to client components
- Cache translation data to minimize API calls
- Follow existing dictionary pattern for consistency

### 6.3 No Fallback Strategy Needed
```typescript
// French defaults in schema eliminate need for fallbacks
const label = vehicleSettings?.fields?.fuelType; // Always has French default
const actionLabel = brandsPage?.fields?.noBrandsFound; // Always has French default
```

### 6.4 CTAs/Links Handling
```typescript
// For interactive elements with href properties
const quickActions = brandsPage?.brandQuickActions?.map(action => ({
  label: action.label,
  description: action.description,
  href: buildDynamicUrl(action.urlPath, { brandSlug, modelSlug }),
  icon: action.icon,
}));
```

---

## 7. DIRECTORY STRUCTURE

### Dictionary Files (Labels - separate from CMS content):
```
src/sanity/dictionary/studio/schemas/labels/
├── vehicle-settings/
│   ├── en.ts
│   ├── fr.ts
│   └── index.ts
└── global-translations/
    ├── en.ts
    ├── fr.ts
    └── index.ts
```

### Schema Files (Labels - separate from CMS content):
```
src/sanity/schemas/labels/
├── vehicle-settings.ts
├── global-translations.ts
└── index.ts
```

### Updated CMS Content Dictionary:
```
src/sanity/dictionary/studio/schemas/singletons/pages/brands-page/
├── en.ts (updated with new fields)
├── fr.ts (updated with new fields)
└── index.ts
```

---

## 8. FILES TO UPDATE (COMPREHENSIVE LIST)

### Frontend Components (30+ files to update):
**Brands Directory:**
- `src/app/(frontend)/gammes/_components/brand-grid.tsx` - Empty state messages
- `src/app/(frontend)/gammes/_components/brand-card.tsx` - Alt text generation
- `src/app/(frontend)/gammes/[brandSlug]/_components/brand-quick-actions.tsx` - All action labels (CTAs)
- `src/app/(frontend)/gammes/[brandSlug]/modeles/[modelSlug]/_components/vehicle-model-navigation.tsx` - Navigation labels (CTAs)
- `src/app/(frontend)/gammes/[brandSlug]/modeles/[modelSlug]/_components/vehicle-model-tabs.tsx` - Tab labels
- `src/app/(frontend)/gammes/[brandSlug]/modeles/[modelSlug]/_components/vehicle-model-quick-actions.tsx` - All action labels (CTAs)
- `src/app/(frontend)/gammes/[brandSlug]/modeles/[modelSlug]/_components/vehicle-model-overview.tsx` - Section headers
- `src/components/brands/models/grid/vehicle-model-grid.tsx` - Empty states
- `src/components/brands/models/card/vehicle-model-card.tsx` - Alt text

**Vehicle Components:**
- `src/components/vehicles/card/constants.ts` - All specification labels
- `src/components/vehicles/filters/components/ConditionContent.tsx` - All condition labels
- `src/components/vehicles/filters/components/ConditionLabel.tsx` - All condition labels
- `src/components/vehicles/filters/components/FuelTypePopoverContent.tsx` - All fuel type labels
- `src/components/vehicles/filters/components/FilterHeader.tsx` - "Filters" title
- `src/components/vehicles/filters/components/FilterChips.tsx` - Remove filter labels
- `src/components/vehicles/toolbar/components/vehicle-count-button.tsx` - Count messages
- `src/components/vehicles/blocks/states/vehicle-display-error.tsx` - Error messages

**Navigation & Global Components:**
- `src/components/global/navigation/navigation-items.tsx` - "No child pages yet"
- `src/components/global/navigation/brand-grid-loader.tsx` - "No brands available"
- `src/components/centers/centers-grid.tsx` - Center messages
- `src/components/shared/form.tsx` - Validation messages
- `src/components/shared/install-demo-button.tsx` - Demo installation text
- `src/components/shared/disable-draft-mode.tsx` - "Disable Draft Mode"
- `src/components/shared/back-button.tsx` - Back button text and aria-label
- `src/components/shared/breadcrumbs.tsx` - Breadcrumb labels

**Layout & Page Components:**
- `src/app/(frontend)/layout.tsx` - Metadata description
- `src/app/(frontend)/page.tsx` - "No Homepage Set" message
- `src/app/(frontend)/vehicles/[id]/_components/vehicle-title.tsx` - Locale formatting

### Sanity Schema Files (2 new label schemas + 1 updated singleton):
- `src/sanity/schemas/singletons/pages/brands-page.ts` - Add brandSettings groups + CTAs/Links
- `src/sanity/schemas/labels/vehicle-settings.ts` - **NEW FILE**
- `src/sanity/schemas/labels/global-translations.ts` - **NEW FILE**
- `src/sanity/schemas/index.ts` - Register new schemas

### Dictionary Files (6 new files + 2 updated):
- `src/sanity/dictionary/studio/schemas/labels/vehicle-settings/en.ts` - **NEW FILE**
- `src/sanity/dictionary/studio/schemas/labels/vehicle-settings/fr.ts` - **NEW FILE**
- `src/sanity/dictionary/studio/schemas/labels/vehicle-settings/index.ts` - **NEW FILE**
- `src/sanity/dictionary/studio/schemas/labels/global-translations/en.ts` - **NEW FILE**
- `src/sanity/dictionary/studio/schemas/labels/global-translations/fr.ts` - **NEW FILE**
- `src/sanity/dictionary/studio/schemas/labels/global-translations/index.ts` - **NEW FILE**
- `src/sanity/dictionary/studio/schemas/singletons/pages/brands-page/en.ts` - **UPDATE**
- `src/sanity/dictionary/studio/schemas/singletons/pages/brands-page/fr.ts` - **UPDATE**

### Query Files (2 new queries + updates):
- `src/sanity/lib/queries/labels/vehicle-settings.ts` - **NEW FILE**
- `src/sanity/lib/queries/labels/global-translations.ts` - **NEW FILE**
- Update existing page queries to include new translation fields

---

## 9. ESTIMATED EFFORT (REVISED)

- **Phase 1**: 30 minutes (Update field groups)
- **Phase 2**: 1-2 hours (Update brands page dictionary + CTAs/Links)
- **Phase 3**: 2-3 hours (Create vehicle settings labels)
- **Phase 4**: 2-3 hours (Create global translations labels)
- **Phase 5**: 5-6 days (Frontend component updates - 30+ files + CTAs/Links)
- **Phase 6**: 2-3 days (Testing and validation)

**Total**: 10-15 days for complete internationalization implementation following dictionary system

---

*This comprehensive checklist captures ALL hardcoded strings found in the frontend application and provides a structured approach to move them to appropriate Sanity label schemas, following the established dictionary system architecture with proper SchemaTranslations interface structure, French defaults, proper field organization, and correct categorization of CTAs/Links vs Simple Labels.*
