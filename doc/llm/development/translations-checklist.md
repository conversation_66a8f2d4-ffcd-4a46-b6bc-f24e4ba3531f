# AutoCorner Website - Comprehensive Translations Checklist

## Overview
This document catalogs all hardcoded text strings found in the frontend that need to be moved to Sanity CMS for proper internationalization support, following the established dictionary system architecture from `doc/llm/sanity/translations/overview.md`.

## Current Translation Status
✅ **Sanity Studio**: Fully translated (EN/FR) with comprehensive dictionary system
❌ **Frontend Components**: Contains extensive hardcoded strings that need to be moved to Sanity
❌ **Global Strings**: Need centralized management following dictionary pattern

---

## 1. BRANDS PAGE SINGLETON UPDATES

### 1.1 Update Brands Page Dictionary (`src/sanity/dictionary/studio/schemas/singletons/pages/brands-page/`)
**Current Status**: Basic page structure exists, needs UI content fields
**Following**: Dictionary system with proper SchemaTranslations interface structure

#### Update English translations (`en.ts`):
```typescript
export const brandsPageTranslations = {
  document: {
    name: "brandsPage",
    title: "Brands Page",
  },

  fields: {
    // Existing fields
    title: "Page Title",
    slug: "URL Slug",
    pageBuilder: "Page Builder",
    seo: "SEO",

    // Brand Actions Group
    brandActions: "Brand Actions",
    bookTestDrive: "Book Test Drive",
    findDealer: "Find Dealer",
    contactSales: "Contact Sales",
    getBrochure: "Get Brochure",
    experienceVehicle: "Experience Vehicle",
    locateNearestDealer: "Locate Nearest Dealer",
    speakWithExpert: "Speak with Expert",
    downloadMaterials: "Download Materials",

    // Vehicle Model Navigation Group
    vehicleModelNavigation: "Vehicle Model Navigation",
    overview: "Overview",
    gallery: "Gallery",
    configure: "Configure",
    compare: "Compare",
    vehicleDetails: "Vehicle Details",
    photosAndVideos: "Photos & Videos",
    buildAndPrice: "Build & Price",
    vsCompetitors: "VS Competitors",

    // Vehicle Model Tabs Group
    vehicleModelTabs: "Vehicle Model Tabs",
    overviewTab: "OVERVIEW",
    techSpecificationTab: "TECH SPECIFICATION",
    vehiclesTab: "VEHICLES",
    galleryTab: "GALLERY",
    offersTab: "OFFERS",
    configureTab: "CONFIGURE",
    technicalDetails: "Technical Details",
    availableModels: "Available Models",
    specialOffers: "Special Offers & Promotions",

    // Vehicle Model Content Group
    vehicleModelContent: "Vehicle Model Content",
    details: "DETAILS",
    interestedIn: "Interested in this",
    specialistsCanHelp: "specialists can help you configure, finance, and schedule a test drive.",
    testDrive: "Test Drive",
    contact: "Contact",
    logoAltText: "logo",
    imageAltText: "image",

    // Brand Grid States Group
    brandGridStates: "Brand Grid States",
    noBrandsFound: "No Brands Found",
    checkBackLater: "Check back later for new brands.",
    noVehiclesFound: "No Vehicles Found",
    checkBackForModels: "Check back later for new models.",
    noBrandsAvailable: "No Brands Available",
  },

  validation: {},

  descriptions: {
    // Existing descriptions
    title: "Title of the Brands page",
    slug: "URL slug for the Brands page",
    pageBuilder: "Page content and layout configuration for Brands presentation",
    seo: "Search engine optimization settings for Brands page",

    // Group descriptions
    brandActions: "Action buttons and links for brand interactions",
    vehicleModelNavigation: "Navigation labels for vehicle model pages",
    vehicleModelTabs: "Tab labels and descriptions for vehicle model sections",
    vehicleModelContent: "Content labels and messages for vehicle model pages",
    brandGridStates: "Messages for empty states and grid conditions",
  },
} as const;
```

#### Update French translations (`fr.ts`):
```typescript
export const brandsPageTranslations = {
  document: {
    name: "brandsPage",
    title: "Page des marques",
  },

  fields: {
    // Existing fields
    title: "Titre de la page",
    slug: "Identifiant URL",
    pageBuilder: "Constructeur de page",
    seo: "SEO",

    // Brand Actions Group
    brandActions: "Actions de marque",
    bookTestDrive: "Réserver un essai",
    findDealer: "Trouver un concessionnaire",
    contactSales: "Contacter les ventes",
    getBrochure: "Obtenir la brochure",
    experienceVehicle: "Découvrir le véhicule",
    locateNearestDealer: "Localiser le concessionnaire le plus proche",
    speakWithExpert: "Parler avec un expert",
    downloadMaterials: "Télécharger les documents",

    // Vehicle Model Navigation Group
    vehicleModelNavigation: "Navigation modèle de véhicule",
    overview: "Aperçu",
    gallery: "Galerie",
    configure: "Configurer",
    compare: "Comparer",
    vehicleDetails: "Détails du véhicule",
    photosAndVideos: "Photos et vidéos",
    buildAndPrice: "Construire et prix",
    vsCompetitors: "VS concurrents",

    // Vehicle Model Tabs Group
    vehicleModelTabs: "Onglets modèle de véhicule",
    overviewTab: "APERÇU",
    techSpecificationTab: "SPÉCIFICATIONS TECHNIQUES",
    vehiclesTab: "VÉHICULES",
    galleryTab: "GALERIE",
    offersTab: "OFFRES",
    configureTab: "CONFIGURER",
    technicalDetails: "Détails techniques",
    availableModels: "Modèles disponibles",
    specialOffers: "Offres spéciales et promotions",

    // Vehicle Model Content Group
    vehicleModelContent: "Contenu modèle de véhicule",
    details: "DÉTAILS",
    interestedIn: "Intéressé par ce",
    specialistsCanHelp: "spécialistes peuvent vous aider à configurer, financer et programmer un essai routier.",
    testDrive: "Essai routier",
    contact: "Contact",
    logoAltText: "logo",
    imageAltText: "image",

    // Brand Grid States Group
    brandGridStates: "États de grille de marques",
    noBrandsFound: "Aucune marque trouvée",
    checkBackLater: "Revenez plus tard pour de nouvelles marques.",
    noVehiclesFound: "Aucun véhicule trouvé",
    checkBackForModels: "Revenez plus tard pour de nouveaux modèles.",
    noBrandsAvailable: "Aucune marque disponible",
  },

  validation: {},

  descriptions: {
    // Existing descriptions
    title: "Titre de la page des marques",
    slug: "Identifiant URL pour la page des marques",
    pageBuilder: "Configuration du contenu et de la mise en page pour la présentation des marques",
    seo: "Paramètres d'optimisation pour les moteurs de recherche de la page des marques",

    // Group descriptions
    brandActions: "Boutons d'action et liens pour les interactions de marque",
    vehicleModelNavigation: "Étiquettes de navigation pour les pages de modèles de véhicules",
    vehicleModelTabs: "Étiquettes d'onglets et descriptions pour les sections de modèles de véhicules",
    vehicleModelContent: "Étiquettes de contenu et messages pour les pages de modèles de véhicules",
    brandGridStates: "Messages pour les états vides et conditions de grille",
  },
} as const;
```

**Group 2: Vehicle Model Navigation** (`vehicleModelNavigation`)
```typescript
// Add to brands-page.ts in brandSettings group
defineField({
  name: "vehicleModelNavigation",
  title: "Navigation modèle de véhicule",
  type: "object",
  group: "brandSettings",
  fields: [
    // Navigation Items
    {
      name: "overview",
      title: "Aperçu",
      type: "string",
      initialValue: "Aperçu"
    },
    {
      name: "gallery",
      title: "Galerie",
      type: "string",
      initialValue: "Galerie"
    },
    {
      name: "configure",
      title: "Configurer",
      type: "string",
      initialValue: "Configurer"
    },
    {
      name: "compare",
      title: "Comparer",
      type: "string",
      initialValue: "Comparer"
    },

    // Navigation Descriptions
    {
      name: "vehicleDetails",
      title: "Détails du véhicule",
      type: "string",
      initialValue: "Détails du véhicule"
    },
    {
      name: "photosAndVideos",
      title: "Photos et vidéos",
      type: "string",
      initialValue: "Photos et vidéos"
    },
    {
      name: "buildAndPrice",
      title: "Construire et prix",
      type: "string",
      initialValue: "Construire et prix"
    },
    {
      name: "vsCompetitors",
      title: "VS concurrents",
      type: "string",
      initialValue: "VS concurrents"
    }
  ]
})
```

**Group 3: Vehicle Model Tabs** (`vehicleModelTabs`)
```typescript
// Add to brands-page.ts in brandSettings group
defineField({
  name: "vehicleModelTabs",
  title: "Onglets modèle de véhicule",
  type: "object",
  group: "brandSettings",
  fields: [
    // Tab Labels
    {
      name: "overview",
      title: "APERÇU",
      type: "string",
      initialValue: "APERÇU"
    },
    {
      name: "techSpecification",
      title: "SPÉCIFICATIONS TECHNIQUES",
      type: "string",
      initialValue: "SPÉCIFICATIONS TECHNIQUES"
    },
    {
      name: "vehicles",
      title: "VÉHICULES",
      type: "string",
      initialValue: "VÉHICULES"
    },
    {
      name: "gallery",
      title: "GALERIE",
      type: "string",
      initialValue: "GALERIE"
    },
    {
      name: "offers",
      title: "OFFRES",
      type: "string",
      initialValue: "OFFRES"
    },
    {
      name: "configure",
      title: "CONFIGURER",
      type: "string",
      initialValue: "CONFIGURER"
    },

    // Tab Descriptions
    {
      name: "vehicleDetails",
      title: "Détails du véhicule",
      type: "string",
      initialValue: "Détails du véhicule"
    },
    {
      name: "technicalDetails",
      title: "Détails techniques",
      type: "string",
      initialValue: "Détails techniques"
    },
    {
      name: "availableModels",
      title: "Modèles disponibles",
      type: "string",
      initialValue: "Modèles disponibles"
    },
    {
      name: "photosAndVideos",
      title: "Photos et vidéos",
      type: "string",
      initialValue: "Photos et vidéos"
    },
    {
      name: "specialOffers",
      title: "Offres spéciales et promotions",
      type: "string",
      initialValue: "Offres spéciales et promotions"
    },
    {
      name: "buildAndPrice",
      title: "Construire et prix",
      type: "string",
      initialValue: "Construire et prix"
    }
  ]
})
```

**Group 4: Vehicle Model Content** (`vehicleModelContent`)
```typescript
// Add to brands-page.ts in brandSettings group
defineField({
  name: "vehicleModelContent",
  title: "Contenu modèle de véhicule",
  type: "object",
  group: "brandSettings",
  fields: [
    // Section Headers
    {
      name: "overview",
      title: "APERÇU",
      type: "string",
      initialValue: "APERÇU"
    },
    {
      name: "details",
      title: "DÉTAILS",
      type: "string",
      initialValue: "DÉTAILS"
    },

    // Contact Section
    {
      name: "interestedIn",
      title: "Intéressé par ce",
      type: "string",
      initialValue: "Intéressé par ce"
    },
    {
      name: "specialistsCanHelp",
      title: "spécialistes peuvent vous aider à configurer, financer et programmer un essai routier.",
      type: "string",
      initialValue: "spécialistes peuvent vous aider à configurer, financer et programmer un essai routier."
    },
    {
      name: "testDrive",
      title: "Essai routier",
      type: "string",
      initialValue: "Essai routier"
    },
    {
      name: "contact",
      title: "Contact",
      type: "string",
      initialValue: "Contact"
    },

    // Alt Text Patterns
    {
      name: "logoAltText",
      title: "logo",
      type: "string",
      initialValue: "logo"
    },
    {
      name: "imageAltText",
      title: "image",
      type: "string",
      initialValue: "image"
    }
  ]
})
```

**Group 5: Brand Grid States** (`brandGridStates`)
```typescript
// Add to brands-page.ts in brandSettings group
defineField({
  name: "brandGridStates",
  title: "États de grille de marques",
  type: "object",
  group: "brandSettings",
  fields: [
    {
      name: "noBrandsFound",
      title: "Aucune marque trouvée",
      type: "string",
      initialValue: "Aucune marque trouvée"
    },
    {
      name: "checkBackLater",
      title: "Revenez plus tard pour de nouvelles marques.",
      type: "string",
      initialValue: "Revenez plus tard pour de nouvelles marques."
    },
    {
      name: "noVehiclesFound",
      title: "Aucun véhicule trouvé",
      type: "string",
      initialValue: "Aucun véhicule trouvé"
    },
    {
      name: "checkBackForModels",
      title: "Revenez plus tard pour de nouveaux modèles.",
      type: "string",
      initialValue: "Revenez plus tard pour de nouveaux modèles."
    },
    {
      name: "noBrandsAvailable",
      title: "Aucune marque disponible",
      type: "string",
      initialValue: "Aucune marque disponible"
    }
  ]
})
```

---

## 2. VEHICLE SETTINGS SINGLETON (NEW)

### 2.1 Create Vehicle Settings Singleton
**File to create**: `src/sanity/schemas/singletons/vehicle-settings.ts`
**Following**: Dictionary system with French defaults and proper field organization

This singleton will contain all vehicle-related global settings and labels:

#### Group 1: Vehicle Specifications (`vehicleSpecs`)
```typescript
// Add to vehicle-settings.ts
defineField({
  name: "vehicleSpecifications",
  title: "Spécifications du véhicule",
  type: "object",
  group: "vehicleSpecs",
  fields: [
    {
      name: "fuelType",
      title: "Type de carburant",
      type: "string",
      initialValue: "Carburant"
    },
    {
      name: "transmission",
      title: "Transmission",
      type: "string",
      initialValue: "Transmission"
    },
    {
      name: "power",
      title: "Puissance",
      type: "string",
      initialValue: "Puissance"
    },
    {
      name: "mileage",
      title: "Kilométrage",
      type: "string",
      initialValue: "Kilométrage"
    },
    {
      name: "year",
      title: "Année",
      type: "string",
      initialValue: "Année"
    },
    {
      name: "condition",
      title: "État",
      type: "string",
      initialValue: "État"
    }
  ]
})
```

#### Group 2: Condition Labels (`conditionLabels`)
```typescript
// Add to vehicle-settings.ts
defineField({
  name: "conditionLabels",
  title: "Étiquettes d'état",
  type: "object",
  group: "conditionLabels",
  fields: [
    {
      name: "new",
      title: "Neuf",
      type: "string",
      initialValue: "Neuf"
    },
    {
      name: "used",
      title: "Occasion",
      type: "string",
      initialValue: "Occasion"
    },
    {
      name: "demonstration",
      title: "Démonstration",
      type: "string",
      initialValue: "Démonstration"
    },
    {
      name: "oldtimer",
      title: "Oldtimer",
      type: "string",
      initialValue: "Oldtimer"
    },
    {
      name: "preRegistered",
      title: "Pré-immatriculé",
      type: "string",
      initialValue: "Pré-immatriculé"
    }
  ]
})
```

---

## 2. VEHICLE SETTINGS SINGLETON (NEW)

### 2.1 Create Vehicle Settings Singleton
**File to create**: `src/sanity/schemas/singletons/vehicle-settings.ts`

This singleton will contain all vehicle-related global settings and labels:

```typescript
export default defineType({
  name: "vehicleSettings",
  title: "Vehicle Settings",
  type: "document",
  fields: [
    // Vehicle Specifications Labels
    defineField({
      name: "specifications",
      title: "Specifications",
      type: "object",
      fields: [
        { name: "fuelType", title: "Fuel Type", type: "string" },
        { name: "transmission", title: "Transmission", type: "string" },
        { name: "power", title: "Power", type: "string" },
        { name: "mileage", title: "Mileage", type: "string" },
        { name: "year", title: "Year", type: "string" },
        { name: "condition", title: "Condition", type: "string" }
      ]
    }),

    // Condition Labels
    defineField({
      name: "conditionLabels",
      title: "Condition Labels",
      type: "object",
      fields: [
        { name: "new", title: "New", type: "string" },
        { name: "used", title: "Used", type: "string" },
        { name: "demonstration", title: "Demonstration", type: "string" },
        { name: "oldtimer", title: "Oldtimer", type: "string" },
        { name: "preRegistered", title: "Pre-registered", type: "string" }
      ]
    }),

    // Fuel Type Labels (comprehensive list)
    defineField({
      name: "fuelTypeLabels",
      title: "Fuel Type Labels",
      type: "object",
      fields: [
        { name: "petrol", title: "Petrol", type: "string" },
        { name: "diesel", title: "Diesel", type: "string" },
        { name: "electric", title: "Electric", type: "string" },
        { name: "hevPetrol", title: "Hybrid Petrol", type: "string" },
        { name: "hevDiesel", title: "Hybrid Diesel", type: "string" },
        { name: "phevPetrol", title: "Plug-in Hybrid Petrol", type: "string" },
        { name: "phevDiesel", title: "Plug-in Hybrid Diesel", type: "string" },
        { name: "cngPetrol", title: "CNG/Petrol", type: "string" },
        { name: "lpgPetrol", title: "LPG/Petrol", type: "string" },
        { name: "hydrogen", title: "Hydrogen", type: "string" },
        { name: "ethanolPetrol", title: "Ethanol", type: "string" },
        { name: "mhevDiesel", title: "Micro-hybrid Diesel", type: "string" },
        { name: "mhevPetrol", title: "Micro-hybrid Petrol", type: "string" },
        { name: "twoStrokeMixture", title: "Two-stroke Engine", type: "string" }
      ]
    }),

    // Fuel Type Group Labels
    defineField({
      name: "fuelTypeGroups",
      title: "Fuel Type Groups",
      type: "object",
      fields: [
        { name: "petrol", title: "Petrol", type: "string" },
        { name: "diesel", title: "Diesel", type: "string" },
        { name: "electric", title: "Electric", type: "string" },
        { name: "hybrid", title: "Hybrid", type: "string" },
        { name: "other", title: "Other", type: "string" }
      ]
    }),

    // Filter UI Labels
    defineField({
      name: "filterLabels",
      title: "Filter Labels",
      type: "object",
      fields: [
        { name: "filters", title: "Filters", type: "string" },
        { name: "clearAll", title: "Clear All", type: "string" },
        { name: "removeFilter", title: "Remove Filter", type: "string" },
        { name: "showResults", title: "Show Results", type: "string" },
        { name: "viewResults", title: "View Results", type: "string" },
        { name: "loading", title: "Loading", type: "string" },
        { name: "vehiclesAvailable", title: "vehicles available", type: "string" },
        { name: "noVehiclesFound", title: "No vehicles found", type: "string" }
      ]
    }),

    // Search Labels
    defineField({
      name: "searchLabels",
      title: "Search Labels",
      type: "object",
      fields: [
        { name: "searchVehicles", title: "Search vehicles", type: "string" },
        { name: "searchPlaceholder", title: "Search placeholder", type: "string" },
        { name: "clearSearch", title: "Clear search", type: "string" },
        { name: "noResultsFound", title: "No results found", type: "string" }
      ]
    })
  ]
})
```

**Hardcoded strings to move**:
- `src/components/vehicles/card/constants.ts`: "Carburant", "Transmission", "Puissance", "Kilométrage"
- `src/components/vehicles/filters/components/ConditionContent.tsx`: All condition labels
- `src/components/vehicles/filters/components/ConditionLabel.tsx`: All condition labels
- `src/components/vehicles/filters/components/FuelTypePopoverContent.tsx`: All fuel type labels and groups
- `src/components/vehicles/filters/components/FilterHeader.tsx`: "Filters"
- `src/components/vehicles/toolbar/components/vehicle-count-button.tsx`: "Voir les résultats", "Chargement...", "véhicules disponibles"

#### Group 3: Fuel Type Labels (`fuelTypeLabels`)
```typescript
// Add to vehicle-settings.ts
defineField({
  name: "fuelTypeLabels",
  title: "Étiquettes de type de carburant",
  type: "object",
  group: "fuelTypeLabels",
  fields: [
    { name: "petrol", title: "Essence", type: "string", initialValue: "Essence" },
    { name: "diesel", title: "Diesel", type: "string", initialValue: "Diesel" },
    { name: "electric", title: "Électrique", type: "string", initialValue: "Électrique" },
    { name: "hevPetrol", title: "Hybride essence", type: "string", initialValue: "Hybride essence" },
    { name: "hevDiesel", title: "Hybride diesel", type: "string", initialValue: "Hybride diesel" },
    { name: "phevPetrol", title: "Hybride rechargeable essence", type: "string", initialValue: "Hybride rechargeable essence" },
    { name: "phevDiesel", title: "Hybride rechargeable diesel", type: "string", initialValue: "Hybride rechargeable diesel" },
    { name: "cngPetrol", title: "GNC/Essence", type: "string", initialValue: "GNC/Essence" },
    { name: "lpgPetrol", title: "GPL/Essence", type: "string", initialValue: "GPL/Essence" },
    { name: "hydrogen", title: "Hydrogène", type: "string", initialValue: "Hydrogène" },
    { name: "ethanolPetrol", title: "Éthanol", type: "string", initialValue: "Éthanol" },
    { name: "mhevDiesel", title: "Micro-hybride diesel", type: "string", initialValue: "Micro-hybride diesel" },
    { name: "mhevPetrol", title: "Micro-hybride essence", type: "string", initialValue: "Micro-hybride essence" },
    { name: "twoStrokeMixture", title: "Moteur à deux temps", type: "string", initialValue: "Moteur à deux temps" }
  ]
})
```

#### Group 4: Fuel Type Groups (`fuelTypeGroups`)
```typescript
// Add to vehicle-settings.ts
defineField({
  name: "fuelTypeGroups",
  title: "Groupes de types de carburant",
  type: "object",
  group: "fuelTypeGroups",
  fields: [
    { name: "petrol", title: "Essence", type: "string", initialValue: "Essence" },
    { name: "diesel", title: "Diesel", type: "string", initialValue: "Diesel" },
    { name: "electric", title: "Électrique", type: "string", initialValue: "Électrique" },
    { name: "hybrid", title: "Hybride", type: "string", initialValue: "Hybride" },
    { name: "other", title: "Autre", type: "string", initialValue: "Autre" }
  ]
})
```

#### Group 5: Vehicle Filter Labels (`vehicleFilterLabels`)
```typescript
// Add to vehicle-settings.ts
defineField({
  name: "vehicleFilterLabels",
  title: "Étiquettes de filtre de véhicule",
  type: "object",
  group: "vehicleFilterLabels",
  fields: [
    { name: "filters", title: "Filtres", type: "string", initialValue: "Filtres" },
    { name: "clearAll", title: "Effacer tout", type: "string", initialValue: "Effacer tout" },
    { name: "removeFilter", title: "Supprimer le filtre", type: "string", initialValue: "Supprimer le filtre" },
    { name: "showResults", title: "Afficher les résultats", type: "string", initialValue: "Voir les résultats" },
    { name: "viewResults", title: "Voir les résultats", type: "string", initialValue: "Voir les résultats" },
    { name: "loading", title: "Chargement", type: "string", initialValue: "Chargement..." },
    { name: "vehiclesAvailable", title: "véhicules disponibles", type: "string", initialValue: "véhicules disponibles" },
    { name: "noVehiclesFound", title: "Aucun véhicule trouvé", type: "string", initialValue: "Aucun véhicule trouvé" }
  ]
})
```

#### Group 6: Vehicle Search Labels (`vehicleSearchLabels`)
```typescript
// Add to vehicle-settings.ts
defineField({
  name: "vehicleSearchLabels",
  title: "Étiquettes de recherche de véhicule",
  type: "object",
  group: "vehicleSearchLabels",
  fields: [
    { name: "searchVehicles", title: "Rechercher des véhicules", type: "string", initialValue: "Rechercher des véhicules" },
    { name: "searchPlaceholder", title: "Placeholder de recherche", type: "string", initialValue: "Rechercher..." },
    { name: "clearSearch", title: "Effacer la recherche", type: "string", initialValue: "Effacer la recherche" },
    { name: "noResultsFound", title: "Aucun résultat trouvé", type: "string", initialValue: "Aucun résultat trouvé" }
  ]
})
```

---

## 3. GLOBAL TRANSLATIONS SINGLETON (NEW)

### 3.1 Create Global Translations Singleton
**File to create**: `src/sanity/schemas/singletons/global-translations.ts`
**Following**: Dictionary system with French defaults and proper field organization

This singleton will contain all global UI strings that don't belong to specific documents:

#### Group 1: Navigation & Layout (`navigation`)
```typescript
// Add to global-translations.ts
defineField({
  name: "navigation",
  title: "Navigation",
  type: "object",
  group: "navigation",
  fields: [
    { name: "menu", title: "Menu", type: "string", initialValue: "Menu" },
    { name: "home", title: "Accueil", type: "string", initialValue: "Accueil" },
    { name: "back", title: "Retour", type: "string", initialValue: "Retour" },
    { name: "close", title: "Fermer", type: "string", initialValue: "Fermer" },
    { name: "noChildPagesYet", title: "Aucune page enfant pour le moment", type: "string", initialValue: "Aucune page enfant pour le moment" }
  ]
})
```

#### Group 2: Common Actions (`commonActions`)
```typescript
// Add to global-translations.ts
defineField({
  name: "commonActions",
  title: "Actions communes",
  type: "object",
  group: "commonActions",
  fields: [
    { name: "search", title: "Rechercher", type: "string", initialValue: "Rechercher" },
    { name: "filter", title: "Filtrer", type: "string", initialValue: "Filtrer" },
    { name: "sort", title: "Trier", type: "string", initialValue: "Trier" },
    { name: "clear", title: "Effacer", type: "string", initialValue: "Effacer" },
    { name: "apply", title: "Appliquer", type: "string", initialValue: "Appliquer" },
    { name: "cancel", title: "Annuler", type: "string", initialValue: "Annuler" },
    { name: "save", title: "Enregistrer", type: "string", initialValue: "Enregistrer" },
    { name: "edit", title: "Modifier", type: "string", initialValue: "Modifier" },
    { name: "delete", title: "Supprimer", type: "string", initialValue: "Supprimer" },
    { name: "view", title: "Voir", type: "string", initialValue: "Voir" },
    { name: "readMore", title: "Lire plus", type: "string", initialValue: "Lire plus" },
    { name: "learnMore", title: "En savoir plus", type: "string", initialValue: "En savoir plus" }
  ]
})
```

#### Group 3: Status Messages & Error Handling (`statusMessages`)
```typescript
// Add to global-translations.ts
defineField({
  name: "statusMessages",
  title: "Messages de statut",
  type: "object",
  group: "statusMessages",
  fields: [
    { name: "loading", title: "Chargement", type: "string", initialValue: "Chargement..." },
    { name: "error", title: "Erreur", type: "string", initialValue: "Erreur" },
    { name: "success", title: "Succès", type: "string", initialValue: "Succès" },
    { name: "noResults", title: "Aucun résultat", type: "string", initialValue: "Aucun résultat" },
    { name: "tryAgain", title: "Réessayer", type: "string", initialValue: "Réessayer" },
    { name: "retry", title: "Réessayer", type: "string", initialValue: "Réessayer" },
    { name: "errorLoadingVehicles", title: "Erreur lors du chargement des véhicules", type: "string", initialValue: "Erreur lors du chargement des véhicules" },
    { name: "unexpectedError", title: "Une erreur inattendue s'est produite", type: "string", initialValue: "Une erreur inattendue s'est produite lors du chargement des véhicules. Veuillez réessayer." },
    { name: "noHomepageSet", title: "Aucune page d'accueil définie", type: "string", initialValue: "Aucune page d'accueil définie..." }
  ]
})
```

#### Group 4: Form Validation (`formValidation`)
```typescript
// Add to global-translations.ts
defineField({
  name: "formValidation",
  title: "Validation de formulaire",
  type: "object",
  group: "formValidation",
  fields: [
    { name: "required", title: "est requis", type: "string", initialValue: "est requis" },
    { name: "invalidEmail", title: "Adresse e-mail invalide", type: "string", initialValue: "Adresse e-mail invalide" },
    { name: "submitError", title: "Erreur de soumission", type: "string", initialValue: "Erreur de soumission" },
    { name: "submitSuccess", title: "Soumission réussie", type: "string", initialValue: "Soumission réussie" }
  ]
})
```

#### Group 5: Development/Demo (`development`)
```typescript
// Add to global-translations.ts
defineField({
  name: "development",
  title: "Développement",
  type: "object",
  group: "development",
  fields: [
    { name: "siteEngine", title: "SiteEngine", type: "string", initialValue: "SiteEngine" },
    { name: "runCommand", title: "Exécuter la commande pour installer le contenu de démonstration", type: "string", initialValue: "Exécuter la commande pour installer le contenu de démonstration" },
    { name: "disableDraftMode", title: "Désactiver le mode brouillon", type: "string", initialValue: "Désactiver le mode brouillon" },
    { name: "defaultMetaDescription", title: "Description méta par défaut", type: "string", initialValue: "Modèle de site web marketing Next.js et Sanity open-source." }
  ]
})
```

#### Group 6: Centers (`centers`)
```typescript
// Add to global-translations.ts
defineField({
  name: "centers",
  title: "Centres",
  type: "object",
  group: "centers",
  fields: [
    { name: "noCentersFound", title: "Aucun centre trouvé", type: "string", initialValue: "Aucun centre trouvé" },
    { name: "forType", title: "pour le type", type: "string", initialValue: "pour le type" }
  ]
})
```

#### Group 7: Shared Components (`sharedComponents`)
```typescript
// Add to global-translations.ts
defineField({
  name: "sharedComponents",
  title: "Composants partagés",
  type: "object",
  group: "sharedComponents",
  fields: [
    { name: "goBackToPrevious", title: "Retourner à la page précédente ou ancre.", type: "string", initialValue: "Retourner à la page précédente ou ancre." },
    { name: "breadcrumbHome", title: "Accueil", type: "string", initialValue: "Accueil" }
  ]
})
```

---

## 4. COMPREHENSIVE HARDCODED STRINGS INVENTORY

### 4.1 Brands Directory - ALL FINDINGS
**Files with hardcoded strings**:

#### `src/app/(frontend)/gammes/_components/brand-grid.tsx`
- "No brands found" → brandGridStates.noBrandsFound
- "Check back later for new brands." → brandGridStates.checkBackLater

#### `src/app/(frontend)/gammes/_components/brand-card.tsx`
- Alt text generation: `${name} logo` → vehicleModelContent.logoAltText
- Fallback character display: `${name?.charAt(0) || "?"}` → (keep as is, dynamic)

#### `src/app/(frontend)/gammes/[brandSlug]/_components/brand-quick-actions.tsx`
- "Book Test Drive" → brandActions.bookTestDrive
- "Find Dealer" → brandActions.findDealer
- "Contact Sales" → brandActions.contactSales
- "Get Brochure" → brandActions.getBrochure
- "Experience the vehicle" → brandActions.experienceVehicle
- "Locate nearest dealer" → brandActions.locateNearestDealer
- "Speak with expert" → brandActions.speakWithExpert
- "Download materials" → brandActions.downloadMaterials

#### `src/app/(frontend)/gammes/[brandSlug]/modeles/[modelSlug]/_components/vehicle-model-navigation.tsx`
- "Overview" → vehicleModelNavigation.overview
- "Gallery" → vehicleModelNavigation.gallery
- "Configure" → vehicleModelNavigation.configure
- "Compare" → vehicleModelNavigation.compare
- "Vehicle details" → vehicleModelNavigation.vehicleDetails
- "Photos & videos" → vehicleModelNavigation.photosAndVideos
- "Build & price" → vehicleModelNavigation.buildAndPrice
- "VS competitors" → vehicleModelNavigation.vsCompetitors

#### `src/app/(frontend)/gammes/[brandSlug]/modeles/[modelSlug]/_components/vehicle-model-tabs.tsx`
- "OVERVIEW" → vehicleModelTabs.overview
- "TECH SPECIFICATION" → vehicleModelTabs.techSpecification
- "VEHICLES" → vehicleModelTabs.vehicles
- "GALLERY" → vehicleModelTabs.gallery
- "OFFERS" → vehicleModelTabs.offers
- "CONFIGURE" → vehicleModelTabs.configure
- "Vehicle details" → vehicleModelTabs.vehicleDetails
- "Technical details" → vehicleModelTabs.technicalDetails
- "Available models" → vehicleModelTabs.availableModels
- "Photos & videos" → vehicleModelTabs.photosAndVideos
- "Special offers & promotions" → vehicleModelTabs.specialOffers
- "Build & price" → vehicleModelTabs.buildAndPrice

#### `src/app/(frontend)/gammes/[brandSlug]/modeles/[modelSlug]/_components/vehicle-model-quick-actions.tsx`
- "Book Test Drive" → brandActions.bookTestDrive
- "Configure" → vehicleModelNavigation.configure
- "Compare" → vehicleModelNavigation.compare
- "Find Dealer" → brandActions.findDealer
- "Contact Sales" → brandActions.contactSales
- "Share Vehicle" → brandActions.shareVehicle
- "Experience this vehicle" → brandActions.experienceVehicle
- "Build & price" → vehicleModelNavigation.buildAndPrice
- "VS competitors" → vehicleModelNavigation.vsCompetitors
- "Locate nearest dealer" → brandActions.locateNearestDealer
- "Speak with expert" → brandActions.speakWithExpert
- "Send to friend" → brandActions.sendToFriend
- "Interested in this" → vehicleModelContent.interestedIn
- "specialists can help you configure, finance, and schedule a test drive." → vehicleModelContent.specialistsCanHelp
- "Test Drive" → vehicleModelContent.testDrive
- "Contact" → vehicleModelContent.contact

#### `src/app/(frontend)/gammes/[brandSlug]/modeles/[modelSlug]/_components/vehicle-model-overview.tsx`
- "OVERVIEW" → vehicleModelContent.overview
- "DETAILS" → vehicleModelContent.details
- Alt text patterns: `${name} logo` → vehicleModelContent.logoAltText
- Alt text patterns: `${name} image` → vehicleModelContent.imageAltText

#### `src/components/brands/models/grid/vehicle-model-grid.tsx`
- "No vehicles found" → brandGridStates.noVehiclesFound
- "Check back later for new models." → brandGridStates.checkBackForModels

#### `src/components/brands/models/card/vehicle-model-card.tsx`
- Alt text: `${brand?.name} logo` → vehicleModelContent.logoAltText
- "Back to Models" → (URL parameter, keep as is)

### 4.2 Vehicle Components - ALL FINDINGS
**Files with hardcoded strings**:

#### `src/components/vehicles/card/constants.ts`
- "Carburant" → vehicleSpecifications.fuelType
- "Transmission" → vehicleSpecifications.transmission
- "Puissance" → vehicleSpecifications.power
- "Kilométrage" → vehicleSpecifications.mileage
- "Neuf" → conditionLabels.new
- "Occasion" → conditionLabels.used
- "Démonstration" → conditionLabels.demonstration
- "Oldtimer" → conditionLabels.oldtimer
- "Pré-immatriculé" → conditionLabels.preRegistered

#### `src/components/vehicles/filters/components/FuelTypePopoverContent.tsx`
- "Essence" → fuelTypeLabels.petrol
- "Diesel" → fuelTypeLabels.diesel
- "Électrique" → fuelTypeLabels.electric
- "Hybride essence" → fuelTypeLabels.hevPetrol
- "Hybride diesel" → fuelTypeLabels.hevDiesel
- "Hybride rechargeable essence" → fuelTypeLabels.phevPetrol
- "Hybride rechargeable diesel" → fuelTypeLabels.phevDiesel
- "GNC/Essence" → fuelTypeLabels.cngPetrol
- "GPL/Essence" → fuelTypeLabels.lpgPetrol
- "Hydrogène" → fuelTypeLabels.hydrogen
- "Éthanol" → fuelTypeLabels.ethanolPetrol
- "Micro-hybride diesel" → fuelTypeLabels.mhevDiesel
- "Micro-hybride essence" → fuelTypeLabels.mhevPetrol
- "Moteur à deux temps" → fuelTypeLabels.twoStrokeMixture
- Group labels: "Essence" → fuelTypeGroups.petrol, "Diesel" → fuelTypeGroups.diesel, "Électrique" → fuelTypeGroups.electric, "Hybride" → fuelTypeGroups.hybrid, "Autre" → fuelTypeGroups.other

#### `src/components/vehicles/filters/components/FilterHeader.tsx`
- "Filters" → vehicleFilterLabels.filters

#### `src/components/vehicles/filters/components/FilterChips.tsx`
- "Remove ${filter.displayKey} filter" → vehicleFilterLabels.removeFilter (aria-label)

#### `src/components/vehicles/toolbar/components/vehicle-count-button.tsx`
- "Voir les résultats" → vehicleFilterLabels.showResults
- "Chargement..." → vehicleFilterLabels.loading
- "${count} véhicules disponibles" → vehicleFilterLabels.vehiclesAvailable

#### `src/components/vehicles/blocks/states/vehicle-display-error.tsx`
- "Error Loading Vehicles" → statusMessages.errorLoadingVehicles
- "An unexpected error occurred while loading vehicles. Please try again." → statusMessages.unexpectedError
- "Retry" → statusMessages.retry

### 4.3 Navigation Components - ALL FINDINGS
**Files with hardcoded strings**:

#### `src/components/global/navigation/navigation-items.tsx`
- "No child pages yet" → navigation.noChildPagesYet

#### `src/components/global/navigation/brand-grid-loader.tsx`
- "No brands available" → brandGridStates.noBrandsAvailable

### 4.4 Centers Components - ALL FINDINGS
**Files with hardcoded strings**:

#### `src/components/centers/centers-grid.tsx`
- "Aucun centre trouvé" → centers.noCentersFound
- "pour le type" → centers.forType

### 4.5 Shared Components - ALL FINDINGS
**Files with hardcoded strings**:

#### `src/components/shared/install-demo-button.tsx`
- "SiteEngine" → development.siteEngine
- "Run the command to install demo content" → development.runCommand
- Command text: "npx sanity dataset import demo-content.tar.gz production" → (keep as is, technical)

#### `src/components/shared/disable-draft-mode.tsx`
- "Disable Draft Mode" → development.disableDraftMode

#### `src/components/shared/form.tsx`
- "is required" → formValidation.required
- "Invalid email address" → formValidation.invalidEmail

#### `src/components/shared/back-button.tsx`
- "Back" → navigation.back
- "Go back to previous page or anchor." → sharedComponents.goBackToPrevious (aria-label)

#### `src/components/shared/breadcrumbs.tsx`
- "Home" → sharedComponents.breadcrumbHome (aria-label)

### 4.6 Layout & Page Components - ALL FINDINGS
**Files with hardcoded strings**:

#### `src/app/(frontend)/layout.tsx`
- "Open-Source Next.js & Sanity Marketing Website Template." → development.defaultMetaDescription

#### `src/app/(frontend)/page.tsx`
- "No Homepage Set..." → statusMessages.noHomepageSet

#### `src/app/(frontend)/vehicles/[id]/_components/vehicle-title.tsx`
- Locale formatting: `vehicle.mileage.toLocaleString("fr-CH")` → (make locale configurable)
- Unit display: " km", " • " → (keep as is, units)

---

## 5. IMPLEMENTATION PLAN (DICTIONARY SYSTEM)

### Phase 1: Update Brands Page Singleton (2-3 days)
1. **Update brands-page.ts** - Add all brandSettings groups following dictionary pattern
2. **Add French defaults** - All fields have French initialValue
3. **Create dictionary files** - Add EN/FR translations for new fields
4. **Update queries** - Include new translation fields

### Phase 2: Create Vehicle Settings Singleton (2-3 days)
1. **Create vehicle-settings.ts** - All vehicle-related translations with French defaults
2. **Add to schema index** - Register new singleton
3. **Create dictionary files** - Add EN/FR translations
4. **Create queries** - Add GraphQL queries for vehicle settings

### Phase 3: Create Global Translations Singleton (2-3 days)
1. **Create global-translations.ts** - All global UI strings with French defaults
2. **Add to schema index** - Register new singleton
3. **Create dictionary files** - Add EN/FR translations
4. **Create queries** - Add GraphQL queries for global strings

### Phase 4: Update Frontend Components (5-6 days)
1. **Create translation hooks** - Following existing pattern
2. **Update 30+ components** - Replace all hardcoded strings with CMS data
3. **No fallbacks needed** - French defaults in schema eliminate need for code fallbacks
4. **Update locale formatting** - Make locale configurable

### Phase 5: Testing & Validation (2-3 days)
1. **Test all languages** - Verify EN/FR switching works across all components
2. **Test default values** - Ensure French defaults display correctly
3. **Performance check** - Verify no performance regression
4. **Accessibility check** - Ensure aria-labels are properly translated

---

## 6. TECHNICAL NOTES (DICTIONARY SYSTEM)

### 6.1 Query Strategy
- Use server-side data fetching (following user preference)
- Pass translation data as props to client components
- Cache translation data to minimize API calls
- Follow existing dictionary pattern for consistency

### 6.2 No Fallback Strategy Needed
```typescript
// French defaults in schema eliminate need for fallbacks
const label = vehicleSettings?.conditionLabels?.new; // Always has French default
const actionLabel = brandSettings?.bookTestDrive; // Always has French default
```

### 6.3 Dictionary Files Structure
```
src/sanity/dictionary/studio/schemas/singletons/
├── vehicle-settings/
│   ├── en.ts
│   └── fr.ts
├── global-translations/
│   ├── en.ts
│   └── fr.ts
└── pages/brands-page/
    ├── en.ts (update existing)
    └── fr.ts (update existing)
```

---

## 7. FILES TO UPDATE (COMPREHENSIVE LIST)

### Frontend Components (30+ files to update):
- `src/app/(frontend)/layout.tsx` - Metadata description
- `src/app/(frontend)/page.tsx` - "No Homepage Set" message
- `src/app/(frontend)/gammes/_components/brand-grid.tsx` - Empty state messages
- `src/app/(frontend)/gammes/_components/brand-card.tsx` - Alt text generation
- `src/app/(frontend)/gammes/[brandSlug]/_components/brand-quick-actions.tsx` - All action labels
- `src/app/(frontend)/gammes/[brandSlug]/modeles/[modelSlug]/_components/vehicle-model-navigation.tsx` - Navigation labels
- `src/app/(frontend)/gammes/[brandSlug]/modeles/[modelSlug]/_components/vehicle-model-tabs.tsx` - Tab labels
- `src/app/(frontend)/gammes/[brandSlug]/modeles/[modelSlug]/_components/vehicle-model-quick-actions.tsx` - All action labels
- `src/app/(frontend)/gammes/[brandSlug]/modeles/[modelSlug]/_components/vehicle-model-overview.tsx` - Section headers
- `src/app/(frontend)/vehicles/[id]/_components/vehicle-title.tsx` - Locale formatting
- `src/components/brands/models/grid/vehicle-model-grid.tsx` - Empty states
- `src/components/brands/models/card/vehicle-model-card.tsx` - Alt text
- `src/components/vehicles/card/constants.ts` - All specification labels
- `src/components/vehicles/filters/components/ConditionContent.tsx` - All condition labels
- `src/components/vehicles/filters/components/ConditionLabel.tsx` - All condition labels
- `src/components/vehicles/filters/components/FuelTypePopoverContent.tsx` - All fuel type labels
- `src/components/vehicles/filters/components/FilterHeader.tsx` - "Filters" title
- `src/components/vehicles/filters/components/FilterChips.tsx` - Remove filter labels
- `src/components/vehicles/toolbar/components/vehicle-count-button.tsx` - Count messages
- `src/components/vehicles/blocks/states/vehicle-display-error.tsx` - Error messages
- `src/components/global/navigation/navigation-items.tsx` - "No child pages yet"
- `src/components/global/navigation/brand-grid-loader.tsx` - "No brands available"
- `src/components/centers/centers-grid.tsx` - Center messages
- `src/components/shared/form.tsx` - Validation messages
- `src/components/shared/install-demo-button.tsx` - Demo installation text
- `src/components/shared/disable-draft-mode.tsx` - "Disable Draft Mode"
- `src/components/shared/back-button.tsx` - Back button text and aria-label
- `src/components/shared/breadcrumbs.tsx` - Breadcrumb labels

### Sanity Schema Files (2 new singletons + 1 update):
- `src/sanity/schemas/singletons/pages/brands-page.ts` - Add brandSettings groups
- `src/sanity/schemas/singletons/vehicle-settings.ts` - **NEW FILE**
- `src/sanity/schemas/singletons/global-translations.ts` - **NEW FILE**
- `src/sanity/schemas/index.ts` - Register new schemas

### Dictionary Files (6 new files):
- `src/sanity/dictionary/studio/schemas/singletons/vehicle-settings/en.ts` - **NEW FILE**
- `src/sanity/dictionary/studio/schemas/singletons/vehicle-settings/fr.ts` - **NEW FILE**
- `src/sanity/dictionary/studio/schemas/singletons/global-translations/en.ts` - **NEW FILE**
- `src/sanity/dictionary/studio/schemas/singletons/global-translations/fr.ts` - **NEW FILE**
- `src/sanity/dictionary/studio/schemas/singletons/pages/brands-page/en.ts` - **UPDATE**
- `src/sanity/dictionary/studio/schemas/singletons/pages/brands-page/fr.ts` - **UPDATE**

### Query Files (2 new queries + updates):
- `src/sanity/lib/queries/singletons/vehicle-settings.ts` - **NEW FILE**
- `src/sanity/lib/queries/singletons/global-translations.ts` - **NEW FILE**
- Update existing page queries to include new translation fields

---

## 8. ESTIMATED EFFORT (REVISED)

- **Phase 1**: 2-3 days (Update brands page singleton with dictionary system)
- **Phase 2**: 2-3 days (Vehicle settings singleton with dictionary system)
- **Phase 3**: 2-3 days (Global translations singleton with dictionary system)
- **Phase 4**: 5-6 days (Frontend component updates - 30+ files)
- **Phase 5**: 2-3 days (Testing and validation)

**Total**: 13-18 days for complete internationalization implementation following dictionary system

---

*This comprehensive checklist captures ALL hardcoded strings found in the frontend application and provides a structured approach to move them to appropriate Sanity singletons, following the established dictionary system architecture with French defaults and proper field organization.*