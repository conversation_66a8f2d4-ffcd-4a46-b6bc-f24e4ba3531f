---
title: "Sanity Translations Overview"
description: "Documentation for the schema-specific translation system in Sanity"
category: "sanity"
subcategory: "translations"
status: "draft"
lastUpdated: "2025-07-16"
tags:
  - "sanity"
  - "translations"
  - "i18n"
  - "schema"
---

# Sanity Translations Overview

## Introduction

This document describes the schema-specific translation system implemented in the Autocorner website's Sanity Studio. The system provides a modular, type-safe approach to managing translations for schema fields, validation messages, and descriptions.

## Table of Contents

- [Architecture](#architecture)
- [Directory Structure](#directory-structure)
- [Translation File Format](#translation-file-format)
- [Implementation Details](#implementation-details)
- [Usage Examples](#usage-examples)
- [Adding New Translations](#adding-new-translations)
- [Best Practices](#best-practices)
- [Related Documentation](#related-documentation)

## Architecture

The translation system follows a factory pattern architecture:

```mermaid
graph TD
    A[Schema Files] --> B[Translation Files]
    B --> C[Translation Factory]
    C --> D[Dictionary Objects]
    D --> A
```

This architecture ensures:

1. **Modularity**: Each schema has its own translation files
2. **Type Safety**: Full TypeScript support with proper inference
3. **Maintainability**: Changes to translation logic affect all schemas
4. **Scalability**: Adding new schemas requires minimal code

## Directory Structure

The translation system follows a consistent directory structure:

```
src/sanity/dictionary/studio/schemas/
├── factory.ts                 # Generic translation factory
├── utils.ts                   # Convenient setup utility
├── index.ts                   # Main schemas translations export
├── documents/                 # Document schema translations
│   ├── index.ts              # Documents translations export
│   ├── page/                 # Page schema translations
│   │   ├── index.ts          # Page translations setup
│   │   ├── en.ts            # English page translations
│   │   └── fr.ts            # French page translations
│   └── ...
├── objects/                   # Object schema translations
│   ├── index.ts              # Objects translations export
│   ├── rich-text/            # Rich text schema translations
│   │   ├── index.ts          # Rich text translations setup
│   │   ├── en.ts            # English rich text translations
│   │   └── fr.ts            # French rich text translations
│   └── ...
└── ...
```

Each schema gets its own directory under the appropriate group (e.g., `objects/`, `documents/`, `misc/`), containing:
- `en.ts`: English translations
- `fr.ts`: French translations
- `index.ts`: Exports and setup

## Translation File Format

### English (en.ts)

```typescript
// English translations for [schema] object
export const [schema]ObjectTranslations = {
  document: { 
    name: "schema", 
    title: "Schema Title" 
  },
  fields: { 
    fieldName: "Field Label",
    // ...more fields
  },
  validation: { 
    fieldRequired: "Field is required",
    // ...more validation messages
  },
  descriptions: { 
    fieldName: "Field description",
    // ...more descriptions
  },
} as const;
```

### French (fr.ts)

```typescript
// French translations for [schema] object
export const [schema]ObjectTranslations = {
  document: { 
    name: "schema", 
    title: "Titre du Schéma" 
  },
  fields: { 
    fieldName: "Titre du Champ",
    // ...more fields
  },
  validation: { 
    fieldRequired: "Le champ est requis",
    // ...more validation messages
  },
  descriptions: { 
    fieldName: "Description du champ",
    // ...more descriptions
  },
} as const;
```

### Index File (index.ts)

```typescript
import * as en from "./en";
import * as fr from "./fr";
import { setupSchemaTranslations } from "../../utils";

const { dict, createField, getTranslations } = setupSchemaTranslations({
  en: en.[schema]ObjectTranslations,
  fr: fr.[schema]ObjectTranslations,
});

export const [schema]ObjectDict = dict;
export const create[Schema]ObjectField = createField;
export const get[Schema]ObjectTranslations = getTranslations;
export type [Schema]ObjectTranslations = typeof en.[schema]ObjectTranslations;
```

## Implementation Details

### Translation Factory

The core of the translation system is the `SchemaTranslationFactory` class, which handles:

1. **Language Detection**: Automatically detects language from environment variables
2. **Fallback Mechanism**: Falls back to English if the requested language is not supported
3. **Type-Safe Access**: Provides type-safe access to translations
4. **Field Creation**: Utility for creating fields with translations

```typescript
// src/sanity/dictionary/studio/schemas/factory.ts
export class SchemaTranslationFactory<T extends SchemaTranslations> {
  private dictionaries: SchemaTranslationDictionaries<T>;
  private currentLanguage: keyof SchemaTranslationDictionaries<T>;

  constructor(dictionaries: SchemaTranslationDictionaries<T>) {
    this.dictionaries = dictionaries;
    this.currentLanguage = this.getCurrentLanguage();
  }

  private getCurrentLanguage(): keyof SchemaTranslationDictionaries<T> {
    const envLang = process.env.NEXT_PUBLIC_DEFAULT_LANGUAGE;
    return envLang && envLang in this.dictionaries ? envLang : "en";
  }

  public getTranslations<K extends keyof SchemaTranslationDictionaries<T>>(
    language?: K,
  ) {
    const targetLanguage = language || this.currentLanguage;
    return this.dictionaries[targetLanguage] || this.dictionaries.en;
  }

  public getDict() {
    return this.getTranslations();
  }

  public createField(fieldName: string, options: Record<string, string> = {}) {
    const dict = this.getDict();
    return {
      name: fieldName,
      title: dict.fields[fieldName],
      description: dict.descriptions[fieldName],
      ...options,
    };
  }

  public getAvailableLanguages() {
    return Object.keys(
      this.dictionaries,
    ) as (keyof SchemaTranslationDictionaries<T>)[];
  }
}
```

### Utility Functions

The `utils.ts` file provides utility functions for setting up translations:

```typescript
// src/sanity/dictionary/studio/schemas/utils.ts
export function setupSchemaTranslations<T extends SchemaTranslations>(
  dictionaries: SchemaTranslationDictionaries<T>,
) {
  const translations = createSchemaTranslations(dictionaries);

  return {
    dict: translations.getDict(),
    createField: (fieldName: string, options: Record<string, string> = {}) =>
      translations.createField(fieldName, options),
    getTranslations: (language?: string) =>
      translations.getTranslations(language),
    availableLanguages: translations.getAvailableLanguages(),
  };
}
```

## Usage Examples

### Basic Usage in Schema Files

```typescript
// src/sanity/schemas/documents/page.ts
import { pageDict } from "../../dictionary/studio/schemas/documents/page";

const { document, fields, validation, descriptions } = pageDict;

export default defineType({
  name: "page",
  title: document.title,
  type: "document",
  fields: [
    defineField({
      name: "title",
      title: fields.title,
      type: "string",
      validation: (rule) => rule.required().error(validation.titleRequired),
      description: descriptions.title,
    }),
    // ...more fields
  ],
});
```

### Using Helper Functions

```typescript
// src/sanity/schemas/documents/page.ts
import { createPageField, pageDict } from "../../dictionary/studio/schemas/documents/page";

export default defineType({
  name: "page",
  title: pageDict.document.title,
  type: "document",
  fields: [
    defineField({
      ...createPageField("title", {
        type: "string",
        validation: (rule) => rule.required().error(pageDict.validation.titleRequired),
      }),
    }),
    // ...more fields
  ],
});
```

## Adding New Translations

To add translations for a new schema:

1. **Create Directory**: Create a directory for the schema under the appropriate group
   ```
   src/sanity/dictionary/studio/schemas/documents/[schema]/
   ```

2. **Create Translation Files**: Create `en.ts` and `fr.ts` files with translations
   ```typescript
   // en.ts
   export const schemaTranslations = {
     document: { name: "schema", title: "Schema Title" },
     fields: { /* fields */ },
     validation: { /* validation messages */ },
     descriptions: { /* descriptions */ },
   } as const;
   ```

3. **Create Index File**: Create `index.ts` file with setup code
   ```typescript
   import * as en from "./en";
   import * as fr from "./fr";
   import { setupSchemaTranslations } from "../../utils";

   const { dict, createField, getTranslations } = setupSchemaTranslations({
     en: en.schemaTranslations,
     fr: fr.schemaTranslations,
   });

   export const schemaDict = dict;
   export const createSchemaField = createField;
   export const getSchemaTranslations = getTranslations;
   export type SchemaTranslations = typeof en.schemaTranslations;
   ```

4. **Export from Parent Index**: Add export to parent `index.ts` file
   ```typescript
   export * from "./[schema]";
   ```

5. **Use in Schema File**: Import and use in schema file
   ```typescript
   import { schemaDict } from "../../dictionary/studio/schemas/documents/[schema]";
   ```

## Best Practices

1. **Naming Conventions**
   - Use camelCase for schema names in exports (e.g., `richTextObjectDict`)
   - Use PascalCase for types (e.g., `RichTextObjectTranslations`)
   - Use consistent naming patterns across all schemas

2. **Translation Structure**
   - Keep translations flat and simple
   - Use dot notation for nested fields (e.g., `"parent.child": "Label"`)
   - Include all fields, validation messages, and descriptions

3. **Code Organization**
   - Keep translation files small and focused
   - Follow the established directory structure
   - Use the utility functions provided

4. **Validation**
   - Ensure all field labels in the schema are present in the translation files
   - Run type/lint checks after each refactor
   - Keep translation keys flat and consistent

## Related Documentation

- [Sanity Schema Structure](../structure/overview.md)
- [Sanity Blocks](../blocks/overview.md)
- [Sanity Queries](../queries/overview.md)